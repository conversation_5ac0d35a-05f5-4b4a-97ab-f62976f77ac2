#pragma once

#include <string>
#include <mutex>
#include <functional>
#include <filesystem>
#include <source_location>
#include <iostream>

#include "Util.hpp"

class Logger {
public:

    /**
     * 日志类型
     */
    enum LogType {
        /** 调试信息 */
        DEBUG = 0,
        /** 一般信息 */
        INFO,
        /** 错误 */
        ERR,
    };

    /**
     * 记录日志
     * @param type 日志类型
     * @param content 日志内容
     * @param sourceLocation 源代码信息
     */
    static inline void log(const LogType& type, const std::string &content, const std::source_location &sourceLocation = std::source_location::current()) {
        std::lock_guard lock(mutex);

        const std::string fileName = std::filesystem::path(sourceLocation.file_name()).filename().string();

        std::stringstream ss;
        ss << "[" << Util::now() << "] "
           << "[" << fileName << ", " << sourceLocation.line() << "] "
           << "[" << std::this_thread::get_id() << "] "
           << "[" << content << "]";

        writer(type, ss.str());
    }

    /**
     * 设置写入器
     * @param writer 写入器
     */
    static inline void setWriter(const std::function<void(const LogType& type, const std::string& log)>& writer) {
        std::lock_guard lock(mutex);

        Logger::writer = writer;
    }

private:

    static inline std::mutex mutex;

    /** 写入器 */
    static inline std::function<void(const LogType &type, const std::string &log)> writer = [](const LogType &type, const std::string &log){
        if (type == DEBUG || type == INFO) {
            std::cout << log << std::endl;
        } else {
            std::cerr << log << std::endl;
        }
    };
};
