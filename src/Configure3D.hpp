#pragma once

#include <vector>

/**
 * 3D工位的配置
 */
class Configure3D final {
public:

    /** 工位控制A 基地址 */
    std::uint16_t workstationControlAAddress {};

    /** 工位控制B 基地址 */
    std::uint16_t workstationControlBAddress {};

    /** 点位B 基地址 */
    std::uint16_t positionBAddress {};

    /** 水平点位数量 */
    std::uint16_t horizontalPositionSize {};

    /** 点位 */
    class Position final {
    public:

        /** Z轴选择 */
        std::uint16_t zAxisMask {};
        /** Z轴坐标 */
        std::array<float,16> zAxisCoordinates {};
    };
    std::vector<Position> positions;
};
