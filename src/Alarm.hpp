#pragma once

#include "Executor.hpp"
#include "ConfigureAlarm.hpp"

/**
 * 报警
 */
class Alarm final : public Executor {
public:
    /**
     * 构造器
     * @param name 名称
     * @param modbusTcpMaster ModbusTCP 主站
     * @param configureAlarm 配置
     * @param alarmInfoChangedCallback 报警状态改变时的回调
     */
    explicit Alarm(std::string name,
                   ModbusTcpMaster &modbusTcpMaster,
                   ConfigureAlarm configureAlarm,
                   std::function<void(std::array<bool, 160> alarms)> alarmInfoChangedCallback)
      : Executor(std::move(name), modbusTcpMaster),
        configureAlarm(std::move(configureAlarm)),
        alarmInfoChangedCallback(std::move(alarmInfoChangedCallback)) {
        start();
    };
    ~Alarm() override {
        stop();
    }

protected:

    /**
     * 初始化
     * @return 执行成功返回true，失败返回false
     */
    bool initialize() override {
        return true;
    }

    /**
     * 执行流程
     */
    void exec() override {
        if (std::vector<std::uint8_t> vectorAlarmInfoA(AlarmInfoA::SIZE); modbusTcpMaster.read(configureAlarm.alarmInfoAAddress, vectorAlarmInfoA)) {
            const auto alarmInfoA = AlarmInfoA::fromByteVector(vectorAlarmInfoA);
            Logger::log(Logger::LogType::DEBUG, name + std::format("读取 {}", alarmInfoA.toString()));

            if (alarmInfoA.alarms != lastAlarmInfoA.alarms) {
                Logger::log(Logger::LogType::INFO, name + std::format("报警状态改变:\n{}", alarmInfoA.toString()));
                alarmInfoChangedCallback(alarmInfoA.alarms);
                lastAlarmInfoA = alarmInfoA;
            }
        }
    }

private:

    /** 配置 */
    ConfigureAlarm configureAlarm;

    /** 报警状态改变时的回调 */
    const std::function<void(const std::array<bool, 160> alarms)> alarmInfoChangedCallback {};

private:

    /** 报警信息 A */
    class AlarmInfoA final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** 心跳 */
            static constexpr std:: uint16_t ALARMS = 0;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 20;

    public:

        /** 心跳 */
        std::array<bool, 160> alarms {};

    public:

        /**
         * 打印字符串
         * @return 字符串
         */
        [[nodiscard]] std::string toString() const {
            std::stringstream ss;
            ss << "报警信息:";
            for (std::size_t i=0;i<alarms.size();i++) {
                if (i%80==0) {
                    ss << std::endl << "       "<< std::format("[{:3}-{:3}] ",i+1, i+80);
                }
                ss << std::to_string(alarms[i]);
                if ((i+1)%16==0) {
                    ss << " ";
                }
            }
            return ss.str();
        }

        /**
         * 从字节容器构造
         * @param byteVector 字节列表
         * @return 报警信息A
         */
        static AlarmInfoA fromByteVector(const std::vector<std::uint8_t>& byteVector) {
            if (byteVector.size() != SIZE) {
                throw std::invalid_argument(std::format("报警信息A 字节列表的大小错误 期望:{}, 实际:{}", SIZE, byteVector.size()));
            }

            AlarmInfoA alarmA {};

            for (std::size_t i = 0; i < alarmA.alarms.size(); ++i) {
                alarmA.alarms[i] = ((byteVector[i / 8] >> (i % 8)) & 1) != 0;
            }

            return alarmA;
        }
    };

private:

    /** 上一次读取到的 报警信息A */
    AlarmInfoA lastAlarmInfoA {};
};