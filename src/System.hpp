#pragma once

#include <algorithm>
#include <utility>

#include "Executor.hpp"
#include "ConfigureSystem.hpp"

/**
 * 系统
 */
class System final : public Executor {
public:

    /** 运行状态 */
    enum class RunningState {
        /** 运行 */
        RUNNING = 100,
        /** 停止 */
        STOPPED = 200,
        /** 暂停 */
        PAUSED = 300,
    };

    /**
     * 构造器
     * @param name 名称
     * @param modbusTcpMaster ModbusTCP 主站
     * @param configureSystem 配置
     * @param runningStateChangedCallback 运行状态改变的回调
     * @param stateWordChangedCallback 状态字改变的回调
     */
    explicit System(std::string name,
                    ModbusTcpMaster &modbusTcpMaster,
                    ConfigureSystem configureSystem,
                    std::function<void(RunningState runningState)> runningStateChangedCallback,
                    std::function<void(std::uint16_t stateWord)> stateWordChangedCallback)
      : Executor(std::move(name), modbusTcpMaster),
        configureSystem(std::move(configureSystem)),
        runningStateChangedCallback(std::move(runningStateChangedCallback)),
        stateWordChangedCallback(std::move(stateWordChangedCallback)) {
        start();
    };
    ~System() override {
        stop();
    }

protected:

    /**
     * 初始化：写入产品编号
     * @return 执行成功返回true，失败返回false
     */
    bool initialize() override {
        std::vector<std::uint8_t> productCode(60);

        std::copy_n(configureSystem.productCode.begin(),
                    (std::min)(productCode.size(),configureSystem.productCode.size()),
                    productCode.begin());

        Logger::log(Logger::INFO, name + std::format("写入产品号:{}", configureSystem.productCode));

        return modbusTcpMaster.write(configureSystem.systemBAddress + SystemB::Offset::PRODUCT_CODE/2, productCode);
    }

    /**
     * 执行流程
     */
    void exec() override {
        // 写入心跳
        if (const auto now = std::chrono::steady_clock::now(); duration_cast<std::chrono::milliseconds>(now - lastLiveBeatTimestamp).count() > LIVE_BEAT_INTERVAL) {

            if (modbusTcpMaster.write(configureSystem.systemBAddress + SystemB::Offset::LIVE_BEAT/2, Util::toByteVector(currentLiveBeat++))) {
                lastLiveBeatTimestamp = now;
                //Logger::log(Logger::DEBUG, name + std::format(" 写入心跳值:{}", currentLiveBeat));
            } else {
                Logger::log(Logger::INFO, name + " 写入心跳失败");
            }

        }

        // 检测 运行状态变化、 状态字变化 事件
        if (std::vector<std::uint8_t> vectorSystemA(SystemA::SIZE); modbusTcpMaster.read(configureSystem.systemAAddress, vectorSystemA)) {
            auto systemA = SystemA::fromByteVector(vectorSystemA);

            Logger::log(Logger::DEBUG, name + " 读取 " + systemA.toString());

            if (systemA.runningState != lastSystemA.runningState) {
                Logger::log(Logger::INFO, name + std::format(" 运行状态变化: {}->{}", lastSystemA.runningState, systemA.runningState));
                runningStateChangedCallback(static_cast<RunningState>(systemA.runningState));
            }

            if (systemA.state != lastSystemA.state) {
                Logger::log(Logger::INFO, name + std::format(" 状态字变化: {}->{}", lastSystemA.state, systemA.state));
                stateWordChangedCallback(systemA.state);
            }

            lastSystemA = systemA;
        } else {
            Logger::log(Logger::INFO, name + " 读取 系统A 失败");
        }
    }

private:

    /** 配置 */
    ConfigureSystem configureSystem;

    /** 运行状态改变时的回调 */
    const std::function<void(RunningState runningState)> runningStateChangedCallback {};

    /** 状态字改变时的回调 */
    const std::function<void(uint16_t stateWord)> stateWordChangedCallback {};

private:

    /** 系统 A */
    class SystemA final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** 心跳 */
            static constexpr std:: uint16_t LIVE_BEAT = 0;

            /** 运行状态 */
            static constexpr std:: uint16_t RUNNING_STATE = 2;

            /** 状态字 */
            static constexpr std:: uint16_t STATE = 4;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 6;

    public:

        /** 心跳 */
        std::uint16_t liveBeat {};

        /** 运行状态 */
        std::uint16_t runningState {};

        /** 状态字 */
        std::uint16_t state {};

    public:

        /**
         * 打印字符串
         * @return 字符串
         */
        [[nodiscard]] std::string toString() const {
            return std::format(
                "系统A  心跳:{} 运行状态:{} 状态字:{}",
                liveBeat,
                runningState,
                state
            );
        }

        /**
         * 从字节容器构造
         * @param byteVector 字节列表
         * @return A
         */
        static SystemA fromByteVector(const std::vector<std::uint8_t>& byteVector) {
            if (byteVector.size() != SIZE) {
                throw std::invalid_argument(std::format("系统A 字节列表的大小错误 期望:{}, 实际:{}", SIZE, byteVector.size()));
            }

            return {
                *reinterpret_cast<const std::uint16_t*>(&byteVector[Offset::LIVE_BEAT]),
                *reinterpret_cast<const std::uint16_t*>(&byteVector[Offset::RUNNING_STATE]),
                *reinterpret_cast<const std::uint16_t*>(&byteVector[Offset::STATE]),
            };
        }
    };

    /** 系统 B */
    class SystemB final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** 心跳 */
            static constexpr std::uint16_t LIVE_BEAT = 0;

            /** 产品编号 */
            static constexpr std::uint16_t PRODUCT_CODE = 2;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 62;

    public:

        /** 心跳 */
        std::uint16_t liveBeat {};

        /** 产品编号 */
        std::string productCode {};

    public:

        /**
         * 转换为字节容器
         * @return 字节容器
         */
        [[nodiscard]] std::vector<std::uint8_t> toByteVector() const {
            std::vector<std::uint8_t> vector;

            Util::appendToByteVector(vector, liveBeat);
            Util::appendToByteVector(vector, productCode);

            return vector;
        }
    };

private:

    /** 上一次发送心跳的时间 */
    std::chrono::time_point<std::chrono::steady_clock> lastLiveBeatTimestamp = std::chrono::steady_clock::now();

    /** 上一次读取到的 系统A */
    SystemA lastSystemA {};

    /** 当前心跳值 */
    std::uint16_t currentLiveBeat {};

    /** 心跳改变时长(ms) */
    const int LIVE_BEAT_INTERVAL = 500;

};