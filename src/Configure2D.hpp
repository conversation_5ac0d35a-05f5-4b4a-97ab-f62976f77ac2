#pragma once

#include <vector>

/**
 * 2D工位的配置
 */
class Configure2D final {
public:

    /** 工位控制A 基地址 */
    std::uint16_t workstationControlAAddress {};

    /** 工位控制B 基地址 */
    std::uint16_t workstationControlBAddress {};

    /** 点位组B 基地址 */
    std::uint16_t positionGroupBAddress {};

    /** 水平点位数量 */
    std::uint16_t horizontalPositionSize {};

    /** 点位组 */
    class PositionGroup final {
    public:

        /** 光源选择 */
        std::uint16_t lightMask {};
        /** 光源坐标 */
        std::array<float, 16> lightCoordinates {};

        /** 点位 */
        class Position final {
        public:

            /** 保持时长(ms) */
            std::uint16_t holdDuration {};
            /** 相机选择 */
            std::uint16_t cameraMask {};
            /** 相机坐标 */
            std::array<float, 16> cameraCoordinates {};
            /** 相机通道类型 */
            enum class CameraChannelType {
                /** 不属于任何通道 */
                NONE = 0,
                /** 通道1 */
                ONE = 1,
                /** 通道2 */
                TWO = 2,
            };
            std::array<CameraChannelType, 16> cameraChannelTypes {};
        };
        std::vector<Position> positions;
    };
    std::vector<PositionGroup> positionGroups;
};
