#pragma once

#include <string>
#include <future>
#include <utility>

#include "ModbusTcpMaster.hpp"

class Executor {
public:

    explicit Executor(std::string name,
                      ModbusTcpMaster &modbusTcpMaster)
      : name(std::move(name)),
        modbusTcpMaster(modbusTcpMaster) {}
    virtual ~Executor() = default;

protected:
    /**
     * 启动
     */
    void start() {
        future = std::async(std::launch::async, [this] {
            while (!stopFlag.load()) {
                if (initialize()) {
                    Logger::log(Logger::INFO, name + " 初始化成功");
                    break;
                } else {
                    Logger::log(Logger::INFO, name + " 初始化失败");
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                }
            }

            while (!stopFlag.load()) {
                exec();
                std::this_thread::yield();
            }
        });
    }

    /**
     * 停止
     */
    void stop() {
        if (future.valid() && std::future_status::ready != future.wait_for(std::chrono::milliseconds(0))) {
            stopFlag.store(true);
            future.get();
        }
    }

    /**
     * 初始化
     */
    virtual bool initialize() = 0;

    /**
     * 执行流程
     */
    virtual void exec() = 0;

    /** 名称 */
    std::string name;

    /** ModbusTCP主站 */
    ModbusTcpMaster &modbusTcpMaster;

private:

    /** 执行线程 */
    std::future<void> future;

    /** 停止标志位 */
    std::atomic<bool> stopFlag = false;
};
