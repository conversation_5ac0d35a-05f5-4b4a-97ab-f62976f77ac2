#pragma once

#include "Logger.hpp"

#include <string>
#include <mutex>
#include <utility>
#include <vector>
#include <functional>
#include <modbus.h>

/**
 * ModbusTCP 中的主站
 */
class ModbusTcpMaster {
public:

    /**
     * 构造器
     * @param ip 从站的IP地址
     * @param port 端口号
     * @param connectionStateChangedCallback 连接状态改变时的回调
     */
    explicit ModbusTcpMaster(std::string ip, const int port, std::function<void(bool)> connectionStateChangedCallback)
        : ip(std::move(ip)),
          port(port),
          connectionStateChangedCallback(std::move(connectionStateChangedCallback)),
          ctx(modbus_new_tcp(ModbusTcpMaster::ip.c_str(), ModbusTcpMaster::port)) {
        connect();
    }

    ~ModbusTcpMaster() {
        disconnect();
        modbus_free(ctx);
    }

    /**
     * 读
     * @param address 起始地址
     * @param content 数据
     * @return 结果
     */
    bool read(const int address, std::vector<std::uint8_t>& content) {
        if (content.size() %2 != 0) {
            Logger::log(Logger::INFO, std::format("Modbus读失败-size错误 {}:{} start:{}, size:{}", ip, port, address, content.size()));
            return false;
        }

        std::lock_guard lock(mutex);

        if (connected.load() || connect()) {
            bool result = true;
            if (content.size() <= CHUNK_SIZE) {
                result = modbus_read_registers(ctx, address, static_cast<int>(content.size()/2), reinterpret_cast<std::uint16_t*>(content.data())) == content.size()/2;
            } else {
                const std::size_t total_size = content.size();
                std::size_t offset = 0;

                while (offset < total_size) {
                    const std::size_t current_chunk_size = (std::min)(CHUNK_SIZE, total_size - offset);

                    if (const int startAddress = address+static_cast<int>(offset/2); modbus_read_registers(ctx, startAddress, static_cast<int>(current_chunk_size/2), reinterpret_cast<std::uint16_t*>(content.data()+offset)) != current_chunk_size/2) {
                        Logger::log(Logger::INFO, std::format("Modbus读失败 {}:{} start:{}, size:{}", ip, port, startAddress, current_chunk_size));
                        result = false;
                        break;
                    }

                    offset += current_chunk_size;
                }
            }

            if (result) {
                return true;
            } else {
                Logger::log(Logger::INFO, std::format("Modbus读失败 {}:{} start:{}, size:{}", ip, port, address, content.size()));
            }
        } else {
            Logger::log(Logger::INFO, std::format("Modbus读失败-连接失败 {}:{} start:{}, size:{}", ip, port, address, content.size()));
        }
        return false;
    }

    /**
     * 写
     * @param address 起始地址
     * @param content 数据
     * @return 成功返回true，失败返回false
     */
    bool write(const int address, const std::vector<std::uint8_t>& content) {
        if (content.size() %2 != 0) {
            Logger::log(Logger::INFO, std::format("Modbus写失败-size错误 {}:{} start:{}, size:{}", ip, port, address, content.size()));
            return false;
        }

        std::lock_guard lock(mutex);

        if (connected.load() || connect()) {
            bool result = true;
            if (content.size() <= CHUNK_SIZE) {
                result = modbus_write_registers(ctx, address, static_cast<int>(content.size()/2), reinterpret_cast<const std::uint16_t*>(content.data())) == content.size()/2;
            } else {
                const std::size_t total_size = content.size();
                std::size_t offset = 0;

                while (offset < total_size) {
                    const std::size_t current_chunk_size = (std::min)(CHUNK_SIZE, total_size - offset);
                    std::vector<std::uint8_t> chunk(content.begin() + static_cast<std::ptrdiff_t>(offset), content.begin() + static_cast<std::ptrdiff_t>(offset + current_chunk_size));

                    if (const int startAddress = address+static_cast<int>(offset/2); modbus_write_registers(ctx, startAddress, static_cast<int>(chunk.size()/2), reinterpret_cast<const std::uint16_t*>(chunk.data())) != chunk.size()/2) {
                        Logger::log(Logger::INFO, std::format("Modbus写失败 {}:{} start:{}, size:{}", ip, port, startAddress, content.size()));
                        result = false;
                        break;
                    }

                    offset += current_chunk_size;
                }
            }

            if (result) {
                return true;
            } else {
                Logger::log(Logger::INFO, std::format("Modbus写失败 {}:{} start:{}, size:{}", ip, port, address, content.size()));
            }
        } else {
            Logger::log(Logger::INFO, std::format("Modbus写失败-连接失败 {}:{} start:{}, size:{}", ip, port, address, content.size()));
        }
        return false;
    }

private:

    /**
     * 连接
     * @return 结果
     */
    bool connect() {
        disconnect();

        if (modbus_connect(ctx) == 0) {
            Logger::log(Logger::INFO, std::format("Modbus连接成功 {}:{}", ip, port));

            if (!connected.load()) {
                connected.store(true);

                if (connectionStateChangedCallback) {
                    connectionStateChangedCallback(connected.load());
                }
            }

            return true;
        } else {
            Logger::log(Logger::INFO, std::format("连接失败 {}:{}", ip, port));
            return false;
        }
    }

    /**
     * 断开连接
     */
    void disconnect() {
        modbus_close(ctx);

        if (connected.load()) {
            connected.store(false);

            if (connectionStateChangedCallback) {
                connectionStateChangedCallback(connected.load());
            }
        }
    }

private:

    /** 从站的 IP 地址 */
    const std::string ip;

    /** 从站的端口号 */
    const int port;

    /** 连接状态改变时的回调 */
    const std::function<void(bool)> connectionStateChangedCallback {};

    /** libmodbus 上下文 */
    modbus_t* ctx;

    std::mutex mutex;

    /** 连接状态 */
    std::atomic<bool> connected = false;

    /** 最大数据长度 */
    static constexpr std::size_t CHUNK_SIZE = 246;
};
