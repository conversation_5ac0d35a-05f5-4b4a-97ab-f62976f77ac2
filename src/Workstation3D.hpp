#pragma once

#include "WorkstationExecutor.hpp"
#include "Configure3D.hpp"

/**
 * 3D 工位
 */
class Workstation3D final : public WorkstationExecutor {
public:

    /**
     * 构造器
     * @param name 名称
     * @param modbusTcpMaster ModbusTCP 主站
     * @param configure3D 配置
     * @param workstationStartedCallback 工位开始时的回调
     * @param positionStartedCallback 点位开始时的回调
     * @param positionArrivedCallback 查询点位结果时的回调
     * @param workstationFinishedCallback 工位结束时的回调
     */
    explicit Workstation3D(std::string name,
                           ModbusTcpMaster &modbusTcpMaster,
                           Configure3D configure3D,
                           std::function<void(const std::string &trayCode)> workstationStartedCallback,
                           std::function<void(std::ptrdiff_t &horizontalPositionIndex, std::size_t &positionIndex)> positionStartedCallback,
                           std::function<void(std::size_t horizontalPositionIndex, std::size_t positionIndex)> positionArrivedCallback,
                           std::function<void()> workstationFinishedCallback)
      : WorkstationExecutor(std::move(name), modbusTcpMaster, configure3D.workstationControlAAddress, configure3D.workstationControlBAddress),
        configure3D(std::move(configure3D)),
        workstationStartedCallback(std::move(workstationStartedCallback)),
        positionStartedCallback(std::move(positionStartedCallback)),
        positionArrivedCallback(std::move(positionArrivedCallback)),
        workstationFinishedCallback(std::move(workstationFinishedCallback)) {
        start();
    }
    ~Workstation3D() override {
        stop();
    }

protected:

    /**
     * 初始化：初始化点位B中所有变量
     * @return 执行成功返回true，失败返回false
     */
    bool initialize() override {
        return WorkstationExecutor::initialize() &&
               modbusTcpMaster.write(configure3D.positionBAddress,PositionB().toByteVector());
    }

    /**
     * 工位开始
     * @param trayCode 托盘编号
     * @return 回复
     */
    std::optional<ResponseType> requestWorkstationStart(const std::string &trayCode) override {
        currentTrayCode = trayCode;
        nextStatus = Status::WAIT_POSITION_WRITE;
        workstationStartedCallback(trayCode);
        return ResponseType::START_WORKSTATION_SUCCESS;
    }

    /**
     * 工位结束
     * @return 回复
     */
    std::optional<ResponseType> requestWorkstationStop() override {
        if (currentStatus == Status::WAIT_WORKSTATION_FINISH) {
            nextStatus = Status::WAIT_WORKSTATION_START;
            workstationFinishedCallback();
            return ResponseType::FINISH_WORKSTATION_SUCCESS;
        }
        return ResponseType::FINISH_WORKSTATION_STATUS_ERROR;
    }

    /**
     * 请求
     * @param request 请求
     * @return 回复
     */
    std::optional<ResponseType> request(const std::uint16_t request) override {
        if (request == static_cast<std::uint16_t>(RequestType::POSITION_WRITE)) {
            Logger::log(Logger::INFO, name + " 请求下一点位");

            if (currentStatus != Status::WAIT_POSITION_WRITE) {
                Logger::log(Logger::ERR, name + std::format(" 状态错误。当前状态为:{}", magic_enum::enum_name(currentStatus)));
                return ResponseType::POSITION_WRITE_STATUS_ERROR;
            }

            requestPosition:
            positionStartedCallback(currentHorizontalPositionIndex, currentPositionIndex);

            Logger::log(Logger::INFO, std::format("中控反馈下一水平点位序号:{} 点位序号:{}",
                currentHorizontalPositionIndex,
                currentPositionIndex));

            if (currentHorizontalPositionIndex < 0) {
                if (!modbusTcpMaster.write(configure3D.positionBAddress+PositionB::Offset::HORIZONTAL_POSITION_INDEX/2, Util::toByteVector(static_cast<std::int16_t>(currentHorizontalPositionIndex)))) {
                    Logger::log(Logger::INFO, name + std::format(" 写入点位失败 水平点位序号:{}", currentHorizontalPositionIndex));
                    return std::nullopt;
                }
                nextStatus = Status::WAIT_WORKSTATION_FINISH;
                Logger::log(Logger::INFO, name + std::format(" 写入下一水平点位序号:{}", currentHorizontalPositionIndex));
            } else {
                if (currentPositionIndex >= configure3D.positions.size()) {
                    Logger::log(Logger::ERR, name + std::format(" 中控反馈了错误的点位序号:{}", currentPositionIndex));
                    goto requestPosition;
                }

                PositionB position {
                    static_cast<std::int16_t>(currentHorizontalPositionIndex),
                    configure3D.positions.at(currentPositionIndex).zAxisMask
                };
                std::ranges::copy(configure3D.positions.at(currentPositionIndex).zAxisCoordinates,
                                  position.zAxisCoordinates.begin());

                if (!modbusTcpMaster.write(configure3D.positionBAddress, position.toByteVector())) {
                    Logger::log(Logger::INFO, name + std::format(" 写入点位失败 水平点位序号:{} 点位序号:{}", currentHorizontalPositionIndex, currentPositionIndex));
                    return std::nullopt;
                }

                Logger::log(Logger::INFO, name + " 点位信息：\n" + position.toString());

                nextStatus = Status::WAIT_POSITION_EXECUTE;
                Logger::log(Logger::INFO, name + std::format("下发水平点位序号:{} 点位序号:{}", currentHorizontalPositionIndex, currentPositionIndex));
            }

            return ResponseType::POSITION_WRITE_SUCCESS;
        }

        if (request == static_cast<std::uint16_t>(RequestType::POSITION_EXECUTED)) {
            Logger::log(Logger::INFO, name + " 点位/点位组已执行");
            if (currentStatus != Status::WAIT_POSITION_EXECUTE) {
                Logger::log(Logger::ERR, name + std::format(" 状态错误。当前状态为:{}", magic_enum::enum_name(currentStatus)));
                return ResponseType::POSITION_EXECUTED_STATUS_ERROR;
            }

            positionArrivedCallback(currentHorizontalPositionIndex, currentPositionIndex);

            nextStatus = Status::WAIT_POSITION_WRITE;
            Logger::log(Logger::INFO, name + std::format(" 中控执行完毕 水平点位序号:{} 点位序号:{}", currentHorizontalPositionIndex, currentPositionIndex));
            return ResponseType::POSITION_EXECUTED_SUCCESS;
        }

        return std::nullopt;
    }

private:

    /** 配置 */
    Configure3D configure3D;

    /** 当前水平点位序号 */
    std::ptrdiff_t currentHorizontalPositionIndex {};

    /** 当前点位序号 */
    std::size_t currentPositionIndex {};

    /** 当前托盘号 */
    std::string currentTrayCode {};

    /** 工位开始时的回调 */
    const std::function<void(const std::string &trayCode)> workstationStartedCallback {};

    /** 点位开始时的回调 */
    const std::function<void(std::ptrdiff_t &horizontalPositionIndex, std::size_t &positionIndex)> positionStartedCallback {};

    /** 点位到达时的回调 */
    const std::function<void(std::size_t horizontalPositionIndex, std::size_t positionIndex)> positionArrivedCallback {};

    /** 工位结束时的回调 */
    const std::function<void()> workstationFinishedCallback {};

private:

    /** 状态A */
    class StatusA final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** Z轴当前坐标 */
            static constexpr std:: uint16_t Z_AXIS_COORDINATES = 0;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 64;

    public:

        /** Z轴当前坐标 */
        std::array<float,16> zAxisCoordinates {};

    public:

        /**
         * 从字节容器构造
         * @param byteVector 字节列表
         * @return A
         */
        static StatusA fromByteVector(const std::vector<std::uint8_t>& byteVector) {
            if (byteVector.size() != SIZE) {
                throw std::invalid_argument(std::format("3D工位 状态A 字节列表的大小错误 期望:{}, 实际:{}", SIZE, byteVector.size()));
            }

            StatusA statusA {};
            std::copy_n(reinterpret_cast<const float*>(&byteVector[Offset::Z_AXIS_COORDINATES]), 16, statusA.zAxisCoordinates.begin());
            return statusA;
        }
    };

    /** 点位B */
    class PositionB final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** 水平点位序号 */
            static constexpr std::uint16_t HORIZONTAL_POSITION_INDEX = 0;

            /** Z轴选择 */
            static constexpr std::uint16_t Z_AXIS_MASK = 2;

            /** Z轴坐标 */
            static constexpr std::uint16_t Z_AXIS_COORDINATES = 4;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 68;

    public:

        /** 水平点位序号 */
        std::int16_t horizontalPositionIndex {};

        /** Z轴选择 */
        std::uint16_t zAxisMask {};

        /** Z轴选择 */
        std::array<float, 16> zAxisCoordinates {};

    public:

        /**
         * 打印字符串
         * @return 字符串
         */
        [[nodiscard]] std::string toString() const {
            std::stringstream ss {};
            for (const auto zAxisCoordinate : zAxisCoordinates) {
                ss << zAxisCoordinate << " ";
            }
            return std::format(
                "3D工位 点位B 水平点位序号:{} Z轴选择:{} Z轴坐标:{}\n",
                horizontalPositionIndex,
                zAxisMask,
                ss.str()
            );
        }

        /**
         * 转换为字节容器
         * @return 字节容器
         */
        [[nodiscard]] std::vector<std::uint8_t> toByteVector() const {
            std::vector<std::uint8_t> vector;

            Util::appendToByteVector(vector, horizontalPositionIndex);
            Util::appendToByteVector(vector, zAxisMask);

            for (const auto zAxisCoordinate : zAxisCoordinates) {
                Util::appendToByteVector(vector, zAxisCoordinate);
            }

            return vector;
        }
    };
};
