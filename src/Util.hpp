#pragma once

#include <string>
#include <vector>
#include <sstream>
#include <chrono>

/**
 * 工具类
 */
class Util {
public:

    Util() = delete;
    ~Util() = delete;

    /**
     * 将 uint16 数组转换为空格分隔的十六进制字符串
     * @param content uint16 数组
     * @return 结果
     */
    static std::string toHexString(const std::vector<uint16_t>& content) {
        std::ostringstream result;
        for (const auto r : content) {
            result << std::hex << std::uppercase << std::setw(4) << std::setfill('0') << r << " ";
        }

        return result.str();
    }

    /**
     * 获取精确到毫秒的当前时间字符串
     * @return 结果
     */
    static std::string now() {
        const auto now = std::chrono::system_clock::now();
        const auto now_milliseconds = std::chrono::floor<std::chrono::milliseconds>(now);
        auto now_zoned = std::chrono::zoned_time{std::chrono::current_zone(), now_milliseconds};
        return std::format("{:%Y-%m-%d %H:%M:%S}", now_zoned);
    }

    /**
     * 转 std::vector<uint8_t>
     * @param value 值
     * @return 结果
     */
    template<class T>
    static std::vector<std::uint8_t> toByteVector(T value) {
        return {reinterpret_cast<std::uint8_t*>(&value), reinterpret_cast<std::uint8_t*>(&value) + sizeof(value)};
    }

    /**
     * 在 std::vector<std::uint8_t> 中附加数据
     * @tparam T 类型
     * @param vector 容器
     * @param value 值
     */
    template<class T>
    static void appendToByteVector(std::vector<std::uint8_t> &vector, const T &value) {
        const auto bytePtr = reinterpret_cast<const std::uint8_t*>(&value);
        vector.insert(vector.end(), bytePtr, bytePtr + sizeof(value));
    }
};
