#pragma once

#include <shared_mutex>
#include <utility>
#include <unordered_map>
#include <string>
#include <format>

/**
 * Z轴偏差
 */
class ZOffsetManager final {
public:

    ZOffsetManager(const ZOffsetManager&) = delete;
    ZOffsetManager(ZOffsetManager&&) = delete;
    ZOffsetManager& operator=(const ZOffsetManager&) = delete;
    ZOffsetManager& operator=(ZOffsetManager&&) = delete;

    static ZOffsetManager& getInstance() {
        static ZOffsetManager instance;
        return instance;
    }

    /** 取/删偏差值时，偏差值不存在 */
    class OffsetNotExistsException final : public std::exception {
    public:
        explicit OffsetNotExistsException(std::string message)
          : message(std::move(message)) {}

        [[nodiscard]] const char* what() const noexcept override {
            return message.c_str();
        }

    private:
        const std::string message;
    };

public:

    /**
     * 获取托盘数据是否已存在
     * @param trayCode 托盘号
     * @return 存在返回true，不存在返回false
     */
    bool exists(const std::string &trayCode) {
        std::shared_lock lock(mutex);

        return offsets.contains(trayCode);
    }

    /**
     * 删除托盘数据
     * @param trayCode 托盘号
     */
    void deleteTray(const std::string &trayCode) {
        std::unique_lock lock(mutex);

        if (!offsets.contains(trayCode)) {
            throw OffsetNotExistsException(std::format("托盘数据不存在 托盘号:{}", trayCode));
        }

        offsets.erase(trayCode);
    }

    /**
     * 更新数据
     * @param trayCode 托盘号
     * @param pieceIndex 工件序号
     * @param value 值
     */
    void updateZOffset(const std::string &trayCode, const std::size_t pieceIndex, const float value) {
        std::unique_lock lock(mutex);

        offsets[trayCode][pieceIndex] = value;
    }

    /**
     * 查询数据
     * @param trayCode
     * @param pieceIndex
     * @return 值
     */
    float getOffset(const std::string &trayCode, const std::size_t pieceIndex) {
        std::shared_lock lock(mutex);

        if (!offsets.contains(trayCode) || !offsets[trayCode].contains(pieceIndex)) {
            throw OffsetNotExistsException(std::format("偏差值不存在 托盘号:{} 序号:{}", trayCode, pieceIndex));
        }

        return offsets[trayCode][pieceIndex];
    }

    /**
     * 清空数据
     */
    void clear() {
        offsets.clear();
    }

private:

    std::shared_mutex mutex;

    /**
     * 第一级：托盘号
     * 第二级：工件序号
     */
    std::unordered_map<std::string, std::unordered_map<std::size_t, float>> offsets;

private:

    ZOffsetManager() = default;
    ~ZOffsetManager() = default;
};
