#pragma once

#include <array>

#include "WorkstationExecutor.hpp"
#include "Configure2D.hpp"

/**
 * 2D 工位
 */
class Workstation2D final : public WorkstationExecutor {
public:

    /**
     * 构造器
     * @param name 名称
     * @param modbusTcpMaster ModbusTCP 主站
     * @param zOffsetManager Z偏差值管理器
     * @param configure2D 工位配置
     * @param disableLaserRanging 停用激光测距
     * @param workstationStartedCallback 工位开始时的回调
     * @param positionGroupStartedCallback 点位组开始时的回调
     * @param queryPositionGroupResultCallback 查询点位组结果时的回调
     * @param workstationFinishedCallback 工位结束时的回调
     */
    explicit Workstation2D(std::string name,
                           ModbusTcpMaster &modbusTcpMaster,
                           ZOffsetManager &zOffsetManager,
                           Configure2D configure2D,
                           std::atomic<bool> &disableLaserRanging,
                           std::function<void(const std::string &trayCode)> workstationStartedCallback,
                           std::function<void(std::size_t horizontalPositionIndex, std::size_t positionGroupIndex)> positionGroupStartedCallback,
                           std::function<void(std::size_t horizontalPositionIndex, std::size_t positionGroupIndex, bool &result)> queryPositionGroupResultCallback,
                           std::function<void()> workstationFinishedCallback)
      : WorkstationExecutor(std::move(name), modbusTcpMaster, configure2D.workstationControlAAddress, configure2D.workstationControlBAddress),
        zOffsetManager(zOffsetManager),
        configure2D(std::move(configure2D)),
        disableLaserRanging(disableLaserRanging),
        workstationStartedCallback(std::move(workstationStartedCallback)),
        positionGroupStartedCallback(std::move(positionGroupStartedCallback)),
        queryPositionGroupResultCallback(std::move(queryPositionGroupResultCallback)),
        workstationFinishedCallback(std::move(workstationFinishedCallback)) {
        start();
    }
    ~Workstation2D() override {
        stop();
    }

protected:

    /**
     * 初始化：初始化点位组B中所有变量
     * @return 执行成功返回true，失败返回false
     */
    bool initialize() override {
        return WorkstationExecutor::initialize() &&
               modbusTcpMaster.write(configure2D.positionGroupBAddress,PositionGroupB().toByteVector());
    }

    /**
     * 工位开始
     * @param trayCode 托盘编号
     * @return 回复
     */
    std::optional<ResponseType> requestWorkstationStart(const std::string &trayCode) override {
        // 查询是否已存在
        if (!disableLaserRanging.load() && !zOffsetManager.exists(trayCode)) {
            Logger::log(Logger::ERR, name + std::format(" 错误:托盘号不存在测距结果 托盘号:{}", trayCode));
            return ResponseType::START_WORKSTATION_TRAY_CODE_MEASURE_NOT_EXISTS;
        }

        currentTrayCode = trayCode;
        currentHorizontalPositionIndex = 0;
        currentPositionGroupIndex = 0;
        nextStatus = Status::WAIT_POSITION_WRITE;
        workstationStartedCallback(trayCode);
        return ResponseType::START_WORKSTATION_SUCCESS;
    }

    /**
     * 工位结束
     * @return 回复
     */
    std::optional<ResponseType> requestWorkstationStop() override {
        if (currentStatus == Status::WAIT_WORKSTATION_FINISH) {
            nextStatus = Status::WAIT_WORKSTATION_START;
            workstationFinishedCallback();
            return ResponseType::FINISH_WORKSTATION_SUCCESS;
        }

        return ResponseType::FINISH_WORKSTATION_STATUS_ERROR;
    }

    /**
     * 请求
     * @param request 请求
     * @return 回复
     */
    std::optional<ResponseType> request(const std::uint16_t request) override {
        if (request == static_cast<std::uint16_t>(RequestType::POSITION_WRITE)) {
            Logger::log(Logger::INFO, name + " 请求下一点位组");

            if (currentStatus != Status::WAIT_POSITION_WRITE) {
                Logger::log(Logger::ERR, name + std::format(" 状态错误。当前状态为:{}", magic_enum::enum_name(currentStatus)));
                return ResponseType::POSITION_WRITE_STATUS_ERROR;
            }

            if (configure2D.horizontalPositionSize == 0) {
                Logger::log(Logger::INFO, name + " 水平点位数量为0，跳过执行点位组");
                currentHorizontalPositionIndex = -1;
            }

            if (currentHorizontalPositionIndex >= 0) {
                PositionGroupB positionGroup {
                    static_cast<std::int16_t>(currentHorizontalPositionIndex),
                    configure2D.positionGroups.at(currentPositionGroupIndex).lightMask,
                    {},
                    static_cast<std::uint16_t>(configure2D.positionGroups.at(currentPositionGroupIndex).positions.size())
                };
                std::ranges::copy(configure2D.positionGroups.at(currentPositionGroupIndex).lightCoordinates,
                                  positionGroup.lightCoordinates.begin());

                for (size_t i=0;i<positionGroup.positionSize;i++) {
                    auto [holdDuration, cameraMask, cameraCoordinates, cameraChannelTypes] = configure2D.positionGroups.at(currentPositionGroupIndex).positions.at(i);
                    positionGroup.positions[i] = {
                        holdDuration,
                        cameraMask
                    };
                    for (size_t j=0;j<cameraCoordinates.size();j++) {
                        auto coordinate = cameraCoordinates.at(j);
                        if (cameraMask >> j & 1) {
                            if (disableLaserRanging.load() || cameraChannelTypes[j] == Configure2D::PositionGroup::Position::CameraChannelType::NONE) {
                                Logger::log(Logger::DEBUG, name + std::format(" 水平点位序号:{} 点位组序号:{} 点位序号:{} 相机序号:{} 不补偿", currentHorizontalPositionIndex, currentPositionGroupIndex, i, j));
                            } else  {
                                // 查询测距补偿
                                float zOffset {};
                                try {
                                    const std::ptrdiff_t pieceIndex = cameraChannelTypes[j] == Configure2D::PositionGroup::Position::CameraChannelType::ONE ?
                                        currentHorizontalPositionIndex :
                                        currentHorizontalPositionIndex + configure2D.horizontalPositionSize/2;

                                    zOffset = zOffsetManager.getOffset(currentTrayCode, pieceIndex);
                                    coordinate += zOffset;
                                    Logger::log(Logger::DEBUG, name + std::format(" 水平点位序号:{} 点位组序号:{} 点位序号:{} 相机序号:{} 补偿值:{} 原值:{}", currentHorizontalPositionIndex, currentPositionGroupIndex, i, j, zOffset, coordinate));
                                } catch (const ZOffsetManager::OffsetNotExistsException &e) {
                                    Logger::log(Logger::ERR, name + std::format(" 点位无测距结果 水平点位序号:{} 点位组序号:{} 点位序号:{} 相机序号:{}",
                                        currentHorizontalPositionIndex,
                                        currentPositionGroupIndex,
                                        i,
                                        j));
                                    return std::nullopt;
                                }
                            }
                        }
                        positionGroup.positions[i].cameraCoordinates[j] = coordinate;
                    }
                }

                if (!modbusTcpMaster.write(configure2D.positionGroupBAddress, positionGroup.toByteVector(true))) {
                    Logger::log(Logger::INFO, name + std::format(" 写入点位组失败 水平点位序号:{} 点位序号:{}", currentHorizontalPositionIndex, currentPositionGroupIndex));
                    return std::nullopt;
                }

                Logger::log(Logger::INFO, name + " 点位组信息：\n" + positionGroup.toString(true));

                positionGroupStartedCallback(currentHorizontalPositionIndex, currentPositionGroupIndex);
                nextStatus = Status::WAIT_POSITION_EXECUTE;

                Logger::log(Logger::INFO, name + std::format(" 下发水平点位序号:{} 点位序号:{}", currentHorizontalPositionIndex, currentPositionGroupIndex));
                return ResponseType::POSITION_WRITE_SUCCESS;
            }

            if (!modbusTcpMaster.write(configure2D.positionGroupBAddress+PositionGroupB::Offset::HORIZONTAL_POSITION_INDEX/2, Util::toByteVector(static_cast<std::int16_t>(currentHorizontalPositionIndex)))) {
                Logger::log(Logger::INFO, name + std::format(" 写入水平点位序号失败 序号:{}", currentHorizontalPositionIndex));
                return std::nullopt;
            }

            Logger::log(Logger::INFO, name + std::format(" 写入水平点位序号:{}", currentHorizontalPositionIndex));
            nextStatus = Status::WAIT_WORKSTATION_FINISH;
            return ResponseType::POSITION_WRITE_SUCCESS;
        }

        if (request == static_cast<std::uint16_t>(RequestType::POSITION_EXECUTED)) {
            Logger::log(Logger::INFO, name + " 点位/点位组已执行");
            if (currentStatus != Status::WAIT_POSITION_EXECUTE) {
                Logger::log(Logger::ERR, name + std::format(" 状态错误。当前状态为:{}", magic_enum::enum_name(currentStatus)));
                return ResponseType::POSITION_EXECUTED_STATUS_ERROR;
            }

            bool result {};
            queryPositionGroupResultCallback(currentHorizontalPositionIndex, currentPositionGroupIndex, result);
            if (result) {
                Logger::log(Logger::INFO, name + std::format(" 中控收图成功 水平点位序号:{} 点位序号:{}", currentHorizontalPositionIndex, currentPositionGroupIndex));

                if (currentPositionGroupIndex < configure2D.positionGroups.size()-1) {
                    currentPositionGroupIndex++;
                } else {
                    currentPositionGroupIndex = 0;
                    if (currentHorizontalPositionIndex < configure2D.horizontalPositionSize-1) {
                        currentHorizontalPositionIndex++;
                    } else {
                        currentHorizontalPositionIndex = -1;
                    }
                }
                nextStatus = Status::WAIT_POSITION_WRITE;
                return ResponseType::POSITION_EXECUTED_SUCCESS;
            }

            Logger::log(Logger::INFO, name + std::format(" 中控收图失败，重试 水平点位序号:{} 点位序号:{}", currentHorizontalPositionIndex, currentPositionGroupIndex));
            positionGroupStartedCallback(currentHorizontalPositionIndex, currentPositionGroupIndex);
            return ResponseType::POSITION_EXECUTED_RECEIVE_IMAGE_FAIL;
        }
        return std::nullopt;
    }

private:

    /** Z轴偏差值管理器 */
    ZOffsetManager &zOffsetManager;

    /** 配置 */
    Configure2D configure2D;

    /** 停用激光测距 */
    std::atomic<bool> &disableLaserRanging;

    /** 当前水平点位序号 */
    std::ptrdiff_t currentHorizontalPositionIndex {};

    /** 当前点位组序号 */
    std::size_t currentPositionGroupIndex {};

    /** 当前托盘号 */
    std::string currentTrayCode {};

    /** 工位开始时的回调 */
    const std::function<void(const std::string &trayCode)> workstationStartedCallback {};

    /** 点位组开始时的回调 */
    const std::function<void(std::size_t horizontalPositionIndex, std::size_t positionGroupIndex)> positionGroupStartedCallback {};

    /** 查询点位组收图结果时的回调 */
    const std::function<void(std::size_t horizontalPositionIndex, std::size_t positionGroupIndex, bool &result)> queryPositionGroupResultCallback {};

    /** 工位结束时的回调 */
    const std::function<void()> workstationFinishedCallback {};

private:

    /** 点位组B */
    class PositionGroupB final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** 水平点位序号 */
            static constexpr std::uint16_t HORIZONTAL_POSITION_INDEX = 0;

            /** 光源选择 */
            static constexpr std::uint16_t LIGHT_MASK = 2;

            /** 光源坐标 */
            static constexpr std::uint16_t LIGHT_COORDINATES = 4;

            /** 点位数量 */
            static constexpr std::uint16_t POSITIONS_SIZE = 68;

            /** 坐标 */
            static constexpr std::uint16_t POSITIONS = 70;

            /** 保持时长 */
            static constexpr std::uint16_t POSITION_GROUP_HOLD_DURATION = 0;

            /** 相机选择 */
            static constexpr std::uint16_t POSITION_GROUP_CAMERA_MASK = 2;

            /** 相机坐标 */
            static constexpr std::uint16_t POSITION_GROUP_CAMERA_COORDINATES = 4;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 750;

    public:

        /** 水平点位序号 */
        std::int16_t horizontalPositionIndex {};

        /** 光源选择 */
        std::uint16_t lightMask {};

        /** 光源坐标 */
        std::array<float, 16> lightCoordinates {};

        /** 点位数量 */
        std::uint16_t positionSize {};

        class Position {
        public:
            /** 保持时长 */
            std::uint16_t holdDuration {};

            /** 相机选择 */
            std::uint16_t cameraMask {};

            /** 相机坐标 */
            std::array<float, 16> cameraCoordinates {};
        };

        /** 点位 */
        std::array<Position, 10> positions {};

    public:

        /**
         * 打印字符串
         * @return 字符串
         */
        [[nodiscard]] std::string toString(const bool onlyValidPositions = false) const {
            std::stringstream lightCoordinatesSS {};
            for (const auto zAxisCoordinate : lightCoordinates) {
                lightCoordinatesSS << zAxisCoordinate << " ";
            }
            auto string = std::format(
                "2D工位 点位组B 水平点位序号:{} 光源选择:{} 光源坐标:{} 点位数量:{}\n",
                horizontalPositionIndex,
                lightMask,
                lightCoordinatesSS.str(),
                positionSize
            );

            std::size_t positionContainedSize = positions.size();

            if (onlyValidPositions) {
                positionContainedSize = positionSize;
            }

            for (int i=0;i<positionContainedSize;i++) {
                std::stringstream ss {};
                for (const auto zAxisCoordinate : positions[i].cameraCoordinates) {
                    ss << zAxisCoordinate << " ";
                }
                string.append(std::format(
                    "点位{} 保持时长:{} 相机选择:{} 相机坐标:{}\n",
                    i,
                    positions[i].holdDuration,
                    positions[i].cameraMask,
                    ss.str()
                ));
            }

            return string;
        }

        /**
         * 转换为字节容器
         * @return 字节容器
         */
        [[nodiscard]] std::vector<std::uint8_t> toByteVector(const bool onlyValidPositions = false) const {
            std::vector<std::uint8_t> vector;

            Util::appendToByteVector(vector, horizontalPositionIndex);
            Util::appendToByteVector(vector, lightMask);
            for (const auto lightCoordinate : lightCoordinates) {
                Util::appendToByteVector(vector, lightCoordinate);
            }
            Util::appendToByteVector(vector, positionSize);

            std::size_t positionContainedSize = positions.size();

            if (onlyValidPositions) {
                positionContainedSize = positionSize;
            }

            for (size_t i=0;i<positionContainedSize;i++) {
                Util::appendToByteVector(vector, positions.at(i).holdDuration);
                Util::appendToByteVector(vector, positions.at(i).cameraMask);
                for (const auto zAxisCoordinate : positions.at(i).cameraCoordinates) {
                    Util::appendToByteVector(vector, zAxisCoordinate);
                }
            }

            return vector;
        }
    };
};
