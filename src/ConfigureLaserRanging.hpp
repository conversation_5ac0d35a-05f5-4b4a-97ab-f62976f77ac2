#pragma once

#include <vector>

/**
 * 激光测距工位的配置
 */
class ConfigureLaserRanging final {
public:

    /** 工位控制A 基地址 */
    std::uint16_t workstationControlAAddress {};

    /** 工位控制B 基地址 */
    std::uint16_t workstationControlBAddress {};

    /** 状态A 基地址 */
    std::uint16_t statusAAddress {};

    /** 点位B 基地址 */
    std::uint16_t positionBAddress {};

    /** 水平点位数量 */
    std::uint16_t horizontalPositionSize {};

    /** 激光测距的基准值 */
    float referenceValue {};
    /** 激光测距的下限值 */
    float lowerLimitValue {};
    /** 激光测距的上限值 */
    float upperLimitValue {};

    /** 传感器数量 */
    enum class SensorSize {
        /** 单传感器 */
        ONE = 1,
        /** 双传感器 */
        TWO = 2,
    } sensorSize = SensorSize::ONE;
};
