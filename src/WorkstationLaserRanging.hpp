#pragma once

#include "WorkstationExecutor.hpp"
#include "ConfigureLaserRanging.hpp"

/**
 * 激光测距工位
 */
class WorkstationLaserRanging final : public WorkstationExecutor {
public:

    /**
     * 构造器
     * @param name 名称
     * @param modbusTcpMaster ModbusTCP 主站
     * @param zOffsetManager Z轴偏差值管理器
     * @param configureLaserRanging 配置
     * @param disableLaserRanging 停用激光测距
     */
    explicit WorkstationLaserRanging(std::string name,
                                     ModbusTcpMaster &modbusTcpMaster,
                                     ZOffsetManager &zOffsetManager,
                                     ConfigureLaserRanging configureLaserRanging,
                                     std::atomic<bool> &disableLaserRanging)
      : WorkstationExecutor(std::move(name), modbusTcpMaster, configureLaserRanging.workstationControlAAddress, configureLaserRanging.workstationControlBAddress),
        zOffsetManager(zOffsetManager),
        configureLaserRanging(std::move(configureLaserRanging)),
        disableLaserRanging(disableLaserRanging) {
        start();
    }
    ~WorkstationLaserRanging() override {
        stop();
    }

protected:

    /**
     * 初始化：初始化点位B中所有变量
     * @return 执行成功返回true，失败返回false
     */
    bool initialize() override {
        return WorkstationExecutor::initialize() &&
               modbusTcpMaster.write(configureLaserRanging.positionBAddress,PositionB().toByteVector());
    }

    /**
     * 工位开始
     * @param trayCode 托盘编号
     * @return 回复
     */
    std::optional<ResponseType> requestWorkstationStart(const std::string &trayCode) override {
        // 查询是否已存在
        if (!disableLaserRanging.load() && zOffsetManager.exists(trayCode)) {
            Logger::log(Logger::ERR, name + std::format(" 错误:托盘号已存在测距结果 托盘号:{}", trayCode));
            return ResponseType::START_WORKSTATION_TRAY_CODE_MEASURE_ALREADY_EXISTS;
        }

        currentTrayCode = trayCode;
        currentHorizontalPositionIndex = 0;
        nextStatus = Status::WAIT_POSITION_WRITE;
        return ResponseType::START_WORKSTATION_SUCCESS;
    }

    /**
     * 工位结束
     * @return 回复
     */
    std::optional<ResponseType> requestWorkstationStop() override {
        if (currentStatus == Status::WAIT_WORKSTATION_FINISH) {
            nextStatus = Status::WAIT_WORKSTATION_START;
            return ResponseType::FINISH_WORKSTATION_SUCCESS;
        }

        return ResponseType::FINISH_WORKSTATION_STATUS_ERROR;
    }

    /**
     * 请求
     * @param request 请求
     * @return 回复
     */
    std::optional<ResponseType> request(const std::uint16_t request) override {
        if (request == static_cast<std::uint16_t>(RequestType::POSITION_WRITE)) {
            Logger::log(Logger::INFO, name + " 请求下一水平点位序号");

            if (currentStatus != Status::WAIT_POSITION_WRITE) {
                Logger::log(Logger::ERR, name + std::format(" 状态错误。当前状态为:{}", magic_enum::enum_name(currentStatus)));
                return ResponseType::POSITION_WRITE_STATUS_ERROR;
            }

            if (disableLaserRanging.load()) {
                currentHorizontalPositionIndex = -1;
            }

            if (!modbusTcpMaster.write(configureLaserRanging.positionBAddress+PositionB::Offset::HORIZONTAL_POSITION_INDEX/2, Util::toByteVector(static_cast<std::int16_t>(currentHorizontalPositionIndex)))) {
                Logger::log(Logger::INFO, name + std::format(" 写入水平点位序号失败 序号:{}", currentHorizontalPositionIndex));
                return std::nullopt;
            }

            if (currentHorizontalPositionIndex >= 0) {
                nextStatus = Status::WAIT_POSITION_EXECUTE;
            } else {
                nextStatus = Status::WAIT_WORKSTATION_FINISH;
            }

            Logger::log(Logger::INFO, name + std::format("下发水平点位序号:{}", currentHorizontalPositionIndex));
            return ResponseType::POSITION_WRITE_SUCCESS;
        }

        if (request == static_cast<std::uint16_t>(RequestType::POSITION_EXECUTED)) {
            Logger::log(Logger::INFO, name + " 请求上传测距结果");

            if (currentStatus != Status::WAIT_POSITION_EXECUTE) {
                Logger::log(Logger::ERR, name + std::format(" 状态错误。当前状态为:{}", magic_enum::enum_name(currentStatus)));
                return ResponseType::POSITION_EXECUTED_STATUS_ERROR;
            }

            if (disableLaserRanging.load()) {
                return ResponseType::POSITION_EXECUTED_SUCCESS;
            }

            std::vector<std::uint8_t> vectorStatusA(StatusA::SIZE);
            if (!modbusTcpMaster.read(configureLaserRanging.statusAAddress, vectorStatusA)) {
                Logger::log(Logger::INFO, name + " 读取测量值失败");
                return std::nullopt;
            }

            const auto [measureValue1, measureValue2] = StatusA::fromByteVector(vectorStatusA);

            if (measureValue1 >= configureLaserRanging.lowerLimitValue &&
                measureValue1 <= configureLaserRanging.upperLimitValue) {
                zOffsetManager.updateZOffset(currentTrayCode, currentHorizontalPositionIndex, measureValue1 - configureLaserRanging.referenceValue);
                Logger::log(Logger::INFO, name + std::format(" 读取测量值1:{} 托盘号:{} 水平点位序号:{}", measureValue1, currentTrayCode, currentHorizontalPositionIndex));

                if (configureLaserRanging.sensorSize == ConfigureLaserRanging::SensorSize::TWO) {
                    if (measureValue2 < configureLaserRanging.lowerLimitValue ||
                        measureValue2 > configureLaserRanging.upperLimitValue) {
                        Logger::log(Logger::ERR, name + std::format(" 测量值2超范围:{} 范围[{}, {}]", measureValue2, configureLaserRanging.lowerLimitValue, configureLaserRanging.upperLimitValue));
                        return ResponseType::POSITION_EXECUTED_MEASURE_OUT_OF_RANGE;
                    }
                    zOffsetManager.updateZOffset(currentTrayCode, configureLaserRanging.horizontalPositionSize + currentHorizontalPositionIndex, measureValue2 - configureLaserRanging.referenceValue);
                    Logger::log(Logger::INFO, name + std::format(" 读取测量值2:{} 托盘号:{} 水平点位序号:{}", measureValue2, currentTrayCode, configureLaserRanging.horizontalPositionSize + currentHorizontalPositionIndex));
                }
                currentHorizontalPositionIndex++;
                if (currentHorizontalPositionIndex >= configureLaserRanging.horizontalPositionSize) {
                    currentHorizontalPositionIndex = -1;
                }

                nextStatus = Status::WAIT_POSITION_WRITE;

                return ResponseType::POSITION_EXECUTED_SUCCESS;
            }
            Logger::log(Logger::ERR, name + std::format(" 测量值1超范围:{} 范围[{}, {}]", measureValue1, configureLaserRanging.lowerLimitValue, configureLaserRanging.upperLimitValue));
            return ResponseType::POSITION_EXECUTED_MEASURE_OUT_OF_RANGE;
        }

        return std::nullopt;
    }

private:

    /** Z轴偏差值管理器 */
    ZOffsetManager &zOffsetManager;

    /** 配置 */
    ConfigureLaserRanging configureLaserRanging;

    /** 停用激光测距 */
    std::atomic<bool> &disableLaserRanging;

    /** 当前水平点位序号 */
    std::ptrdiff_t currentHorizontalPositionIndex {};

    /** 当前托盘号 */
    std::string currentTrayCode {};

private:

    /** 状态A */
    class StatusA final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** 测量值1 */
            static constexpr std:: uint16_t MEASURE_VALUE_1 = 0;

            /** 测量值2 */
            static constexpr std:: uint16_t MEASURE_VALUE_2 = 4;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 8;

    public:

        /** 测量值1 */
        float measureValue1 {};

        /** 测量值2 */
        float measureValue2 {};

    public:

        /**
         * 从字节容器构造
         * @param byteVector 字节列表
         * @return A
         */
        static StatusA fromByteVector(const std::vector<std::uint8_t>& byteVector) {
            if (byteVector.size() != SIZE) {
                throw std::invalid_argument(std::format("激光测距工位 状态A 字节列表的大小错误 期望:{}, 实际:{}", SIZE, byteVector.size()));
            }

            return {
                *reinterpret_cast<const float*>(&byteVector[Offset::MEASURE_VALUE_1]),
                *reinterpret_cast<const float*>(&byteVector[Offset::MEASURE_VALUE_2]),
            };
        }
    };

    /** 点位B */
    class PositionB final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** 水平点位序号 */
            static constexpr std::uint16_t HORIZONTAL_POSITION_INDEX = 0;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 2;

    public:

        /** 水平点位序号 */
        std::int16_t horizontalPositionIndex {};

    public:

        /**
         * 从字节容器构造
         * @param byteVector 字节列表
         * @return B
         */
        static PositionB fromByteVector(const std::vector<std::uint8_t>& byteVector) {
            if (byteVector.size() != SIZE) {
                throw std::invalid_argument(std::format("激光测距工位 点位B 字节列表的大小错误 期望:{}, 实际:{}", SIZE, byteVector.size()));
            }

            return {*reinterpret_cast<const std::int16_t*>(&byteVector[Offset::HORIZONTAL_POSITION_INDEX])
            };
        }

        /**
         * 转换为字节容器
         * @return 字节容器
         */
        [[nodiscard]] std::vector<std::uint8_t> toByteVector() const {
            std::vector<std::uint8_t> vector;

            Util::appendToByteVector(vector, horizontalPositionIndex);

            return vector;
        }
    };
};
