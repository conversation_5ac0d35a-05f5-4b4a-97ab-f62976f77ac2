#pragma once

#include <utility>

#include "Util.hpp"
#include "ZOffsetManager.hpp"
#include "magic_enum.hpp"

/**
 * 工位控制
 */
class WorkstationExecutor : public Executor {
public:

    /**
     * 构造器
     * @param name 名称
     * @param modbusTcpMaster ModbusTCP 主站
     * @param workstationControlAAddress 工位控制A 基地址
     * @param workstationControlBAddress 工位控制B 基地址
     */
    explicit WorkstationExecutor(std::string name,
                                 ModbusTcpMaster &modbusTcpMaster,
                                 const std::uint16_t workstationControlAAddress,
                                 const std::uint16_t workstationControlBAddress)
      : Executor(std::move(name), modbusTcpMaster),
        workstationControlAAddress(workstationControlAAddress),
        workstationControlBAddress(workstationControlBAddress) {}

protected:

    /** 请求类型 */
    enum class RequestType {
        /** 开始工位 */
        START_WORKSTATION = 100,
        /** 结束工位 */
        FINISH_WORKSTATION = 200,
        /** 下发点位/点位组 */
        POSITION_WRITE = 300,
        /** 点位/点位组已执行 */
        POSITION_EXECUTED = 400,
        /** 判定结果 */
        RESULT_WRITE = 500,
        /** 分拣结果 */
        SORT_RESULT = 600
    };

    /** 响应类型 */
    enum class ResponseType {
        /** 开始工位 */
        START_WORKSTATION_SUCCESS = 100,                            // 工位开始
        START_WORKSTATION_TRAY_CODE_EMPTY = 101,                    // 托盘号为空
        START_WORKSTATION_TRAY_CODE_MEASURE_ALREADY_EXISTS = 102,   // 测距结果已存在
        START_WORKSTATION_TRAY_CODE_MEASURE_NOT_EXISTS = 103,       // 测距结果不存在
        START_WORKSTATION_STATUS_ERROR = 199,                       // 状态错误

        /** 结束工位 */
        FINISH_WORKSTATION_SUCCESS = 200,                           // 工位结束
        FINISH_WORKSTATION_STATUS_ERROR = 299,                      // 状态错误

        /** 下发点位/点位组 */
        POSITION_WRITE_SUCCESS = 300,                               // 点位/点位组已下发
        POSITION_WRITE_STATUS_ERROR = 399,                          // 状态错误

        /** 点位/点位组已执行 */
        POSITION_EXECUTED_SUCCESS = 400,                            // 点位/点位组执行成功
        POSITION_EXECUTED_MEASURE_OUT_OF_RANGE = 401,               // 激光测距测量值超限
        POSITION_EXECUTED_RECEIVE_IMAGE_FAIL = 402,                 // 收图失败
        POSITION_EXECUTED_STATUS_ERROR = 499,                       // 状态错误

        /** 判定结果 */
        RESULT_WRITE_SUCCESS = 500,                                 // 判定结果已下发
        RESULT_WRITE_RESULT_NOT_EXISTS = 501,                       // 判定结果不存在
        RESULT_WRITE_STATUS_ERROR = 599,                            // 状态错误

        /** 分拣结果 */
        SORT_RESULT_SUCCESS = 600,                                  // 分拣结果已上传
        SORT_RESULT_NOT_CORRECT = 601,                              // 分拣结果错误
        SORT_RESULT_STATUS_ERROR = 699,                             // 状态错误
    };

    /** 状态 */
    enum class Status {
        WAIT_WORKSTATION_START,         // 待开始
        WAIT_POSITION_WRITE,            // 待下发点位
        WAIT_RESULT_WRITE,              // 待下发判定结果
        WAIT_SORT_RESULT,               // 待上传分拣结果
        WAIT_POSITION_EXECUTE,          // 待执行点位
        WAIT_WORKSTATION_FINISH,        // 待结束
    }
    /** 当前状态 */
    currentStatus = Status::WAIT_WORKSTATION_START,
    /** 下一状态 */
    nextStatus = currentStatus;

    /**
     * 初始化：初始化工位控制B中所有变量
     * @return 执行成功返回true，失败返回false
     */
    bool initialize() override {
        return modbusTcpMaster.write(workstationControlBAddress,std::vector<std::uint8_t >(WorkstationControlB::SIZE));
    }

    /**
     * 执行流程
     */
    void exec() override {
        std::vector<std::uint8_t> vectorWorkstationControlA(WorkstationControlA::SIZE);
        std::vector<std::uint8_t> vectorWorkstationControlB(WorkstationControlB::SIZE);

        if (modbusTcpMaster.read(workstationControlAAddress, vectorWorkstationControlA) &&
            modbusTcpMaster.read(workstationControlBAddress, vectorWorkstationControlB)) {
            const auto workstationControlA = WorkstationControlA::fromByteVector(vectorWorkstationControlA);
            const auto workstationControlB = WorkstationControlB::fromByteVector(vectorWorkstationControlB);

            Logger::log(Logger::DEBUG, name + std::format(" 读取：\n{}\n{}", workstationControlA.toString(), workstationControlB.toString()));

            if (workstationControlB.response == 0) {
                if (workstationControlA.request == static_cast<std::uint16_t>(RequestType::START_WORKSTATION)) {
                    Logger::log(Logger::INFO, name + std::format(" 请求工位开始 托盘号:{}", workstationControlA.trayCode));
                    if (std::strlen(workstationControlA.trayCode.c_str()) == 0) {
                        Logger::log(Logger::ERR, name + " 托盘号为空");
                        if (!modbusTcpMaster.write(workstationControlBAddress + WorkstationControlB::Offset::RESPONSE/2, Util::toByteVector(static_cast<std::uint16_t>(ResponseType::START_WORKSTATION_TRAY_CODE_EMPTY)))) {
                            Logger::log(Logger::INFO, name + " 写入响应失败");
                        }
                        return;
                    }

                    if (auto response = requestWorkstationStart(workstationControlA.trayCode); response.has_value()) {
                        if (!modbusTcpMaster.write(workstationControlBAddress + WorkstationControlB::Offset::RESPONSE/2, Util::toByteVector(static_cast<std::uint16_t>(response.value())))) {
                            Logger::log(Logger::INFO, name + " 写入响应失败");
                            return;
                        }
                        Logger::log(Logger::DEBUG, name + std::format(" 向PLC写入响应:{}", static_cast<std::uint16_t>(response.value())));
                    }
                } else if (workstationControlA.request == static_cast<std::uint16_t>(RequestType::FINISH_WORKSTATION)) {
                    Logger::log(Logger::INFO, name + " 请求工位结束");
                    if (auto response = requestWorkstationStop(); response.has_value()) {
                        if (!modbusTcpMaster.write(workstationControlBAddress + WorkstationControlB::Offset::RESPONSE/2, Util::toByteVector(static_cast<std::uint16_t>(response.value())))) {
                            Logger::log(Logger::INFO, name + " 写入响应失败");
                            return;
                        }
                        Logger::log(Logger::DEBUG, name + std::format(" 向PLC写入响应:{}", static_cast<std::uint16_t>(response.value())));
                    }
                } else {
                    if (auto response = request(workstationControlA.request); response.has_value()) {
                        if (!modbusTcpMaster.write(workstationControlBAddress + WorkstationControlB::Offset::RESPONSE/2, Util::toByteVector(static_cast<std::uint16_t>(response.value())))) {
                            Logger::log(Logger::INFO, name + " 写入响应(Response)失败");
                            return;
                        }
                        Logger::log(Logger::DEBUG, name + std::format(" 向PLC写入响应:{}", static_cast<std::uint16_t>(response.value())));
                    }
                }
            } else if (workstationControlA.request == 0) {
                if (!modbusTcpMaster.write(workstationControlBAddress + WorkstationControlB::Offset::RESPONSE/2, Util::toByteVector(static_cast<std::uint16_t>(0)))) {
                    Logger::log(Logger::INFO, name + " 写入响应复位失败");
                    return;
                }
                Logger::log(Logger::INFO, name + std::format(" 握手结束 {}->{}", workstationControlB.response, 0));
                if (currentStatus != nextStatus) {
                    Logger::log(Logger::INFO, name + std::format(" 状态切换 {}->{}", magic_enum::enum_name(currentStatus), magic_enum::enum_name(nextStatus)));
                    currentStatus = nextStatus;
                }
            }
        }
    }

    /**
     * 工位开始
     * @param trayCode 托盘编号
     * @return 回复
     */
    virtual std::optional<ResponseType> requestWorkstationStart(const std::string &trayCode) = 0;

    /**
     * 工位结束
     * @return 回复
     */
    virtual std::optional<ResponseType> requestWorkstationStop() = 0;

    /**
     * 请求
     * @param request 请求
     * @return 回复
     */
    virtual std::optional<ResponseType> request(std::uint16_t request) = 0;

private:

    /** 工位控制A 基地址 */
    std::uint16_t workstationControlAAddress {};

    /** 工位控制B 基地址 */
    std::uint16_t workstationControlBAddress {};

private:

    /** 工位控制 A */
    class WorkstationControlA final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** 托盘编号 */
            static constexpr std:: uint16_t TRAY_CODE = 0;

            /** 请求 */
            static constexpr std::uint16_t REQUEST = 60;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 62;

    public:

        /** 托盘编号 */
        std::string trayCode;

        /** 请求 */
        std::uint16_t request;

    public:

        /**
         * 从字节容器构造
         * @param byteVector 字节列表
         * @return A
         */
        static WorkstationControlA fromByteVector(const std::vector<std::uint8_t>& byteVector) {
            if (byteVector.size() != SIZE) {
                throw std::invalid_argument(std::format("工位控制 A 字节列表的大小错误 期望:{}, 实际:{}", SIZE, byteVector.size()));
            }

            return {
                {reinterpret_cast<const char*>(byteVector.data() + Offset::TRAY_CODE), 60},
                *reinterpret_cast<const std::uint16_t*>(&byteVector[Offset::REQUEST]),
            };
        }

        /**
         * 转换为字符串
         * @return 字符串
         */
        [[nodiscard]] std::string toString() const {
            return std::format("工位控制A TrayCode:{} Request:{}", std::string(trayCode.c_str()), request);
        }
    };

    /** 工位控制 B */
    class WorkstationControlB final {
    public:

        /** 各变量的偏移 */
        class Offset {
        public:

            /** 响应 */
            static constexpr std::uint16_t RESPONSE = 0;
        };

        /** 大小 */
        static constexpr std::size_t SIZE = 2;

    public:

        /** 响应 */
        std::uint16_t response;

    public:

        /**
         * 从字节容器构造
         * @param byteVector 字节列表
         * @return B
         */
        static WorkstationControlB fromByteVector(const std::vector<std::uint8_t>& byteVector) {
            if (byteVector.size() != SIZE) {
                throw std::invalid_argument(std::format("工位控制B 字节列表的大小错误 期望:{}, 实际:{}", SIZE, byteVector.size()));
            }

            return {*reinterpret_cast<const std::uint16_t*>(&byteVector[Offset::RESPONSE])
            };
        }

        /**
         * 转换为字符串
         * @return 字符串
         */
        [[nodiscard]] std::string toString() const {
            return std::format("工位控制B Response:{}", response);
        }
    };
};
