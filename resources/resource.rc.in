#include <windows.h>

VS_VERSION_INFO VERSIONINFO
	FILEVERSION ${PROJECT_VERSION_MAJOR},${PROJECT_VERSION_MINOR},${PROJECT_VERSION_PATCH}
	PRODUCTVERSION ${PROJECT_VERSION_MAJOR},${PROJECT_VERSION_MINOR},${PROJECT_VERSION_PATCH}
	FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
	FILEFLAGS VS_FF_DEBUG
#else
	FILEFLAGS 0x0L
#endif
	FILEOS VOS_NT_WINDOWS32
	FILETYPE VFT_DLL
	FILESUBTYPE 0x0L
	BEGIN
		BLOCK "StringFileInfo"
		BEGIN
			BLOCK "040904E4"
			BEGIN
				VALUE "CompanyName", "${TARGET_COMPANY_NAME}"
				VALUE "FileDescription", "${TARGET_FILE_DESCRIPTION}"
				VALUE "FileVersion", "${PROJECT_VERSION}"
				VALUE "LegalCopyright", "${TARGET_COPYRIGHT}"
				VALUE "OriginalFilename", "${TARGET_ORIGINAL_FILENAME}.dll"
				VALUE "ProductName", "${PROJECT_NAME}"
				VALUE "ProductVersion", "${PROJECT_VERSION}"
			END
		END
		BLOCK "VarFileInfo"
		BEGIN
			VALUE "Translation", 0x0409, 1252
		END
	END
