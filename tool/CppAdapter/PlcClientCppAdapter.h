#pragma once

#include <string>
#include <vector>
#include <array>
#include <functional>
#include <iomanip>
#include <sstream>

#include "magic_enum.hpp"
#include "json.hpp"

/**
 * PlcClient 库的 C++ 适配器
 */
class PlcClientCppAdapter final {
public:

    PlcClientCppAdapter(const PlcClientCppAdapter&) = delete;
    PlcClientCppAdapter& operator=(const PlcClientCppAdapter&) = delete;
    PlcClientCppAdapter(PlcClientCppAdapter&&) = delete;
    PlcClientCppAdapter& operator=(PlcClientCppAdapter&&) = delete;

    static PlcClientCppAdapter& getInstance();

public:

    /**
     * PlcClient 配置
     */
    struct Configure {

        /** Modbus 连接 */
        struct ModbusChannel {
            /** IP地址 */
            std::string ip {};
            /** 端口号 */
            std::uint16_t port = 502;
        };
        std::vector<ModbusChannel> modbusChannels;

        /** 系统 */
        struct System {
            /** Modbus 连接 */
            std::size_t modbusChannelIndex {};

            /** 系统A 基地址 */
            std::uint16_t systemAAddress {};
            /** 系统B 基地址 */
            std::uint16_t systemBAddress {};

            /** 产品编号 */
            std::string productCode {};
        } system;

        /** 激光测距工位 */
        struct WorkstationLaserRanging {
            /** Modbus 连接 */
            std::size_t modbusChannelIndex {};

            /** 工位控制A 基地址 */
            std::uint16_t workstationControlAAddress {};
            /** 工位控制B 基地址 */
            std::uint16_t workstationControlBAddress {};
            /** 状态A 基地址 */
            std::uint16_t statusAAddress {};
            /** 点位B 基地址 */
            std::uint16_t positionBAddress {};

            /** 水平点位数量 */
            std::uint16_t horizontalPositionSize {};

            /** 激光测距的基准值 */
            float referenceValue {};
            /** 激光测距的下限值 */
            float lowerLimitValue {};
            /** 激光测距的上限值 */
            float upperLimitValue {};

            /** 传感器数量 */
            enum class SensorSize {
                /** 单传感器 */
                ONE = 1,
                /** 双传感器 */
                TWO = 2,
            } sensorSize = SensorSize::ONE;
        };
        std::vector<WorkstationLaserRanging> workstationLaserRangings;

        /** 2D工位 */
        struct Workstation2D {
            /** Modbus 连接 */
            std::size_t modbusChannelIndex {};

            /** 工位控制A 基地址 */
            std::uint16_t workstationControlAAddress {};
            /** 工位控制B 基地址 */
            std::uint16_t workstationControlBAddress {};
            /** 点位组B 基地址 */
            std::uint16_t positionGroupBAddress {};

            /** 水平点位数量 */
            std::uint16_t horizontalPositionSize {};

            /** 点位组 */
            struct PositionGroup {
                /** 光源选择 */
                std::uint16_t lightMask {};
                /** 光源坐标 */
                std::array<float, 16> lightCoordinates {};
                /** 点位 */
                struct Position {
                    /** 保持时长(ms) */
                    std::uint16_t holdDuration {};
                    /** 相机选择 */
                    std::uint16_t cameraMask {};
                    /** 相机坐标 */
                    std::array<float, 16> cameraCoordinates {};
                    /** 相机通道类型 */
                    enum class CameraChannelType {
                        /** 不属于任何通道 */
                        NONE = 0,
                        /** 通道1 */
                        ONE = 1,
                        /** 通道2 */
                        TWO = 2,
                    };
                    std::array<CameraChannelType, 16> cameraChannelTypes {};
                    /** 图号 */
                    std::array<int, 16> cameraImageNumbers {};
                };
                std::vector<Position> positions;
            };
            std::vector<PositionGroup> positionGroups;
        };
        std::vector<Workstation2D> workstation2Ds;

        /** 3D工位 */
        struct Workstation3D {
            /** Modbus 连接 */
            std::size_t modbusChannelIndex {};

            /** 工位控制A 基地址 */
            std::uint16_t workstationControlAAddress {};
            /** 工位控制B 基地址 */
            std::uint16_t workstationControlBAddress {};
            /** 点位B 基地址 */
            std::uint16_t positionBAddress {};

            /** 水平点位数量 */
            uint16_t horizontalPositionSize {};

            /** 点位 */
            struct Position {
                /** Z轴选择 */
                std::uint16_t zAxisMask {};
                /** Z轴坐标 */
                std::array<float, 16> zAxisCoordinates {};
            };
            std::vector<Position> positions;
        };
        std::vector<Workstation3D> workstation3Ds;

        /** 下料工位 */
        struct WorkstationUnload {
            /** Modbus 连接 */
            std::size_t modbusChannelIndex {};

            /** 工位控制A 基地址 */
            std::uint16_t workstationControlAAddress {};
            /** 工位控制B 基地址 */
            std::uint16_t workstationControlBAddress {};
            /** 分拣结果A 基地址 */
            std::uint16_t sortResultAAddress {};
            /** 判定结果B 基地址 */
            std::uint16_t resultBAddress {};
        };
        std::vector<WorkstationUnload> workstationUnloads;

        /** 报警 */
        struct Alarm {
            /** Modbus 连接 */
            std::size_t modbusChannelIndex {};

            /** 报警信息A */
            std::uint16_t alarmInfoAAddress {};
        } alarm;

        /**
         * 从 JSON 文件加载配置
         * @param fileName JSON文件路径
         * @return Configure 实例
         */
        static Configure fromJson(const std::string& fileName);

        /**
         * 转换为字符串
         * @return 结果
         */
        [[nodiscard]] std::string toString() const {
            std::ostringstream oss;

            oss << "Modbus Channels:" << std::endl;
            for (int i=0;i<modbusChannels.size();i++) {
                oss << "    " << i << ": " << modbusChannels.at(i).ip << ":" << modbusChannels.at(i).port << std::endl;
            }
            oss << std::endl;

            oss << "System:" << std::endl;
            oss << "    " << "ModbusChannelIndex: " << system.modbusChannelIndex << std::endl;
            oss << "    " << "SystemAAddress: " << system.systemAAddress << std::endl;
            oss << "    " << "SystemBAddress: " << system.systemBAddress << std::endl;
            oss << "    " << "ProductCode: " << system.productCode << std::endl;
            oss << std::endl;

            for (int i=0;i<workstationLaserRangings.size();i++) {
                oss << "WorkstationLaserRanging " << i+1 << ":" << std::endl;
                oss << "    " << "ModbusChannelIndex: " << workstationLaserRangings.at(i).modbusChannelIndex << std::endl;
                oss << "    " << "WorkstationControlAAddress: " << workstationLaserRangings.at(i).workstationControlAAddress << std::endl;
                oss << "    " << "WorkstationControlBAddress: " << workstationLaserRangings.at(i).workstationControlBAddress << std::endl;
                oss << "    " << "StatusAAddress: " << workstationLaserRangings.at(i).statusAAddress << std::endl;
                oss << "    " << "PositionBAddress: " << workstationLaserRangings.at(i).positionBAddress << std::endl;
                oss << "    " << "HorizontalPositionSize: " << workstationLaserRangings.at(i).horizontalPositionSize << std::endl;
                oss << "    " << "ReferenceValue: " << workstationLaserRangings.at(i).referenceValue << std::endl;
                oss << "    " << "LowerLimit: " << workstationLaserRangings.at(i).lowerLimitValue << std::endl;
                oss << "    " << "UpperLimit: " << workstationLaserRangings.at(i).upperLimitValue << std::endl;
                oss << "    " << "SensorSize: " << magic_enum::enum_name(workstationLaserRangings.at(i).sensorSize) << std::endl;
            }
            oss << std::endl;

            for (int i=0;i<workstation2Ds.size();i++) {
                oss << "Workstation2D " << i+1 << ":" << std::endl;
                oss << "    " << "ModbusChannelIndex: " << workstation2Ds.at(i).modbusChannelIndex << std::endl;
                oss << "    " << "WorkstationControlAAddress: " << workstation2Ds.at(i).workstationControlAAddress << std::endl;
                oss << "    " << "WorkstationControlBAddress: " << workstation2Ds.at(i).workstationControlBAddress << std::endl;
                oss << "    " << "PositionGroupBAddress: " << workstation2Ds.at(i).positionGroupBAddress << std::endl;
                oss << "    " << "HorizontalPositionSize: " << workstation2Ds.at(i).horizontalPositionSize << std::endl;
                oss << "    " << "PositionGroup: " << std::endl;
                for (int j=0;j<workstation2Ds.at(i).positionGroups.size();j++) {
                    oss << "    " << "    " << std::setw(2) << j << ": " << "LightMask: " << workstation2Ds.at(i).positionGroups.at(j).lightMask << std::endl;
                    oss << "    " << "    " << "    " << "LightCoordinate: ";
                    oss << "[";
                    for (size_t k=0; k<workstation2Ds.at(i).positionGroups.at(j).lightCoordinates.size(); k++) {
                        oss << workstation2Ds.at(i).positionGroups.at(j).lightCoordinates[k];
                        if (k != workstation2Ds.at(i).positionGroups.at(j).lightCoordinates.size() - 1) {
                            oss << ", ";
                        }
                    }
                    oss << "]" << std::endl;
                    oss << "    " << "    " << "    " << "Position: " << std::endl;
                    for (int k=0;k<workstation2Ds.at(i).positionGroups.at(j).positions.size();k++) {
                        oss << "    " << "    " << "    " << "    " << std::setw(2) << k << ": " << std::endl;
                        oss << "    " << "    " << "    " << "    " << "    " << "HoldDuration: " << workstation2Ds.at(i).positionGroups.at(j).positions.at(k).holdDuration << std::endl;
                        oss << "    " << "    " << "    " << "    " << "    " << "CameraMask: " << workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraMask << std::endl;
                        oss << "    " << "    " << "    " << "    " << "    " << "CameraCoordinate: ";
                        oss << "[";
                        for (size_t l=0; l<workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraCoordinates.size(); l++) {
                            oss << workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraCoordinates[l];
                            if (k != workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraCoordinates.size() - 1) {
                                oss << ", ";
                            }
                        }
                        oss << "]" << std::endl;
                        oss << "    " << "    " << "    " << "    " << "    " << "CameraChannelTypes: ";
                        oss << "[";
                        for (size_t l=0; l<workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraChannelTypes.size(); l++) {
                            oss << magic_enum::enum_name(workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraChannelTypes[l]);
                            if (k != workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraChannelTypes.size() - 1) {
                                oss << ", ";
                            }
                        }
                        oss << "]" << std::endl;
                        oss << "    " << "    " << "    " << "    " << "    " << "CameraImageNumbers: ";
                        oss << "[";
                        for (size_t l=0; l<workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraImageNumbers.size(); l++) {
                            oss << workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraImageNumbers[l];
                            if (k != workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraImageNumbers.size() - 1) {
                                oss << ", ";
                            }
                        }
                        oss << "]" << std::endl;
                    }
                }
            }
            oss << std::endl;

            for (int i=0;i<workstation3Ds.size();i++) {
                oss << "Workstation3D " << i+1 << ":" << std::endl;
                oss << "    " << "ModbusChannelIndex: " << workstation3Ds.at(i).modbusChannelIndex << std::endl;
                oss << "    " << "WorkstationControlAAddress: " << workstation3Ds.at(i).workstationControlAAddress << std::endl;
                oss << "    " << "WorkstationControlBAddress: " << workstation3Ds.at(i).workstationControlBAddress << std::endl;
                oss << "    " << "PositionBAddress: " << workstation3Ds.at(i).positionBAddress << std::endl;
                oss << "    " << "HorizontalPositionSize: " << workstation3Ds.at(i).horizontalPositionSize << std::endl;
                oss << "    " << "Position: " << std::endl;
                for (int j=0;j<workstation3Ds.at(i).positions.size();j++) {
                    oss << "    " << "    " << std::setw(2) << j << ": " << "ZAxisMask: " << workstation3Ds.at(i).positions.at(j).zAxisMask << std::endl;
                    oss << "    " << "    " << "    " << "ZAxisCoordinate: ";
                    oss << "[";
                    for (size_t k=0; k<workstation3Ds.at(i).positions.at(j).zAxisCoordinates.size(); k++) {
                        oss << workstation3Ds.at(i).positions.at(j).zAxisCoordinates[k];
                        if (k != workstation3Ds.at(i).positions.at(j).zAxisCoordinates.size() - 1) {
                            oss << ", ";
                        }
                    }
                    oss << "]" << std::endl;
                }
            }
            oss << std::endl;

            for (int i=0;i<workstationUnloads.size();i++) {
                oss << "WorkstationUnloads " << i+1 << ":" << std::endl;
                oss << "    " << "ModbusChannelIndex: " << workstationUnloads.at(i).modbusChannelIndex << std::endl;
                oss << "    " << "WorkstationControlAAddress: " << workstationUnloads.at(i).workstationControlAAddress << std::endl;
                oss << "    " << "WorkstationControlBAddress: " << workstationUnloads.at(i).workstationControlBAddress << std::endl;
                oss << "    " << "SortResultAAddress: " << workstationUnloads.at(i).sortResultAAddress << std::endl;
                oss << "    " << "ResultBAddress: " << workstationUnloads.at(i).resultBAddress << std::endl;
            }
            oss << std::endl;

            oss << "Alarm:" << std::endl;
            oss << "    " << "ModbusChannelIndex: " << alarm.modbusChannelIndex << std::endl;
            oss << "    " << "AlarmInfoAAddress: " << alarm.alarmInfoAAddress << std::endl;
            oss << std::endl;

            return oss.str();
        }
    };

    /** 运行状态 */
    enum class RunningState {
        /** 运行 */
        RUNNING = 100,
        /** 停止 */
        STOPPED = 200,
        /** 暂停 */
        PAUSED = 300,
    };

    enum class LogType {
        /** 调试信息 */
        DEBUG = 0,
        /** 一般信息 */
        INFO,
        /** 错误 */
        ERR,
    };

    /**
     * 启动
     * @param configure 配置
     * @return 成功返回 true，失败返回 false
     */
    bool start(const Configure &configure);

    /**
     * 停止
     */
    void stop();

    /**
     * 获取库的版本号
     * @return 版本号
     */
    std::string getVersion();

public:

    /**
     * Modbus 通信状态改变
     * @param modbusChannelIndex Modbus连接的序号
     * @param connected true: 已连接  false: 已断开
     */
    std::function<void(std::size_t modbusChannelIndex, bool connected)> modbusConnectionStateChanged;

    /**
     * 运行状态改变
     * @param runningState 运行状态
     */
    std::function<void(const RunningState &runningState)> runningStateChanged;

    /**
     * 状态字改变
     * @param stateWord 状态字
     */
    std::function<void(std::uint16_t stateWord)> stateWordChanged;

    /**
     * 报警状态改变
     * @param alarms 报警状态
     */
    std::function<void(const std::array<bool, 160> &alarms)> alarmStateChanged;

    /**
     * 日志
     * @param logType 日志类型
     * @param log 日志内容
     */
    std::function<void(const LogType &logType, const std::string &log)> log;
    
public:

    /**
     * 2D工位 工位开始
     * @param workstation2DIndex 当前工位在2D工位中的序号
     * @param trayCode 托盘号
     */
    std::function<void(std::size_t workstation2DIndex,
                       const std::string &trayCode)> workstationStarted2D;

    /**
     * 2D工位 点位组开始
     * @param workstation2DIndex 当前工位在2D工位中的序号
     * @param horizontalPositionIndex 水平点位序号
     * @param positionGroupIndex 点位组的序号
     */
    std::function<void(std::size_t workstation2DIndex,
                       std::size_t horizontalPositionIndex,
                       std::size_t positionGroupIndex)> positionGroupStarted2D;

    /**
     * 2D工位 查询点位组的收图结果
     * @param workstation2DIndex 当前工位在2D工位中的序号
     * @param horizontalPositionIndex 水平点位序号
     * @param positionGroupIndex 点位组的序号
     * @param result 收图成功返回true，失败返回false
     */
    std::function<void(std::size_t workstation2DIndex,
                       std::size_t horizontalPositionIndex,
                       std::size_t positionGroupIndex,
                       bool &result)> queryPositionGroupResult2D;

    /**
     * 2D工位 工位结束
     * @param workstation2DIndex 当前工位在2D工位中的序号
     */
    std::function<void(std::size_t workstation2DIndex)> workstationFinished2D;

public:

    /**
     * 3D工位 工位开始
     * @param workstation3DIndex 当前工位在3D工位中的序号
     * @param trayCode 托盘号
     */
    std::function<void(std::size_t workstation3DIndex,
                       const std::string &trayCode)> workstationStarted3D;

    /**
     * 3D工位 点位开始
     * @param workstation3DIndex 当前工位在3D工位中的序号
     * @param horizontalPositionIndex 水平点位序号。<0时表示当前工位结束。
     * @param positionIndex 光学面序号
     */
    std::function<void(std::size_t workstation3DIndex,
                       std::ptrdiff_t &horizontalPositionIndex,
                       std::size_t &positionIndex)> positionStarted3D;

    /**
     * 3D工位 点位到达。点位结束时返回
     * @param workstation3DIndex 当前工位在3D工位中的序号
     * @param horizontalPositionIndex 水平点位序号
     * @param positionIndex 光学面序号
     */
    std::function<void(std::size_t workstation3DIndex,
                       std::size_t horizontalPositionIndex,
                       std::size_t positionIndex)> positionArrived3D;

    /**
     * 3D工位 工位结束
     * @param workstation3DIndex 当前工位在3D工位中的序号
     */
    std::function<void(std::size_t workstation3DIndex)> workstationFinished3D;

public:

    /**
     * 下料工位 查询判定结果
     * @param workstationUnloadIndex 当前工位在下料工位中的序号
     * @param trayCode 托盘号
     * @param result 判定结果
     */
    std::function<void(std::size_t workstationUnloadIndex,
                       const std::string &trayCode,
                       std::array<std::uint8_t, 400> &result)> queryResult;

    /**
     * 分拣结果
     */
    class SortResult {
    public:
        /**
         * 托盘类型 1:OK 2:疑似NG 3:NG
         */
        std::uint16_t trayType {};

        /** 托盘编号（从1开始） */
        std::uint16_t trayIndex {};

        /** 穴位编号（从1开始） */
        std::uint16_t holeIndex {};
    };

    /**
     * 下料工位 上传分拣结果
     * @param workstationUnloadIndex 当前工位在下料工位中的序号
     * @param trayCode 托盘号
     * @param sortResults 分拣结果
     * @param result 上传结果
     */
    std::function<void(std::size_t workstationUnloadIndex,
                       const std::string &trayCode,
                       std::vector<SortResult> &sortResults,
                       bool &result)> sortResult;

private:

    PlcClientCppAdapter() = default;
    ~PlcClientCppAdapter() = default;
};

namespace nlohmann {
    template <>
    struct adl_serializer<PlcClientCppAdapter::Configure::ModbusChannel> {
        static void from_json(const json& j, PlcClientCppAdapter::Configure::ModbusChannel& c) {
            j.at("ip").get_to(c.ip);
            j.at("port").get_to(c.port);
        }
    };

    template <>
    struct adl_serializer<PlcClientCppAdapter::Configure::System> {
        static void from_json(const json& j, PlcClientCppAdapter::Configure::System& s) {
            j.at("modbusChannelIndex").get_to(s.modbusChannelIndex);
            j.at("systemAAddress").get_to(s.systemAAddress);
            j.at("systemBAddress").get_to(s.systemBAddress);
            j.at("productCode").get_to(s.productCode);
        }
    };

    template <>
    struct adl_serializer<PlcClientCppAdapter::Configure::WorkstationLaserRanging> {
        static void from_json(const json& j, PlcClientCppAdapter::Configure::WorkstationLaserRanging& w) {
            j.at("modbusChannelIndex").get_to(w.modbusChannelIndex);
            j.at("workstationControlAAddress").get_to(w.workstationControlAAddress);
            j.at("workstationControlBAddress").get_to(w.workstationControlBAddress);
            j.at("statusAAddress").get_to(w.statusAAddress);
            j.at("positionBAddress").get_to(w.positionBAddress);
            j.at("horizontalPositionSize").get_to(w.horizontalPositionSize);
            j.at("referenceValue").get_to(w.referenceValue);
            j.at("lowerLimitValue").get_to(w.lowerLimitValue);
            j.at("upperLimitValue").get_to(w.upperLimitValue);
            w.sensorSize = static_cast<PlcClientCppAdapter::Configure::WorkstationLaserRanging::SensorSize>(j.at("sensorSize").get<int>());
        }
    };

    template <>
    struct adl_serializer<PlcClientCppAdapter::Configure::Workstation2D::PositionGroup::Position> {
        static void from_json(const json& j, PlcClientCppAdapter::Configure::Workstation2D::PositionGroup::Position& p) {
            j.at("holdDuration").get_to(p.holdDuration);
            j.at("cameraMask").get_to(p.cameraMask);

            auto& cameraCoords = j.at("cameraCoordinates");
            for (size_t i = 0; i < cameraCoords.size() && i < 16; ++i)
                p.cameraCoordinates[i] = cameraCoords[i].get<float>();

            auto& channelTypes = j.at("cameraChannelTypes");
            for (size_t i = 0; i < channelTypes.size() && i < 16; ++i)
                p.cameraChannelTypes[i] = static_cast<typename PlcClientCppAdapter::Configure::Workstation2D::PositionGroup::Position::CameraChannelType>(channelTypes[i].get<int>());

            auto& cameraImgs = j.at("imageNumber");
            for (size_t i = 0; i < cameraImgs.size() && i < 16; ++i)
                p.cameraImageNumbers[i] = cameraImgs[i].get<int>();
        }
    };

    template <>
    struct adl_serializer<PlcClientCppAdapter::Configure::Workstation2D::PositionGroup> {
        static void from_json(const json& j, PlcClientCppAdapter::Configure::Workstation2D::PositionGroup& pg) {
            j.at("lightMask").get_to(pg.lightMask);

            auto& lightCoords = j.at("lightCoordinates");
            for (size_t i = 0; i < lightCoords.size() && i < 16; ++i)
                pg.lightCoordinates[i] = lightCoords[i].get<float>();

            j.at("positions").get_to(pg.positions);
        }
    };

    template <>
    struct adl_serializer<PlcClientCppAdapter::Configure::Workstation2D> {
        static void from_json(const json& j, PlcClientCppAdapter::Configure::Workstation2D& w) {
            j.at("modbusChannelIndex").get_to(w.modbusChannelIndex);
            j.at("workstationControlAAddress").get_to(w.workstationControlAAddress);
            j.at("workstationControlBAddress").get_to(w.workstationControlBAddress);
            j.at("positionGroupBAddress").get_to(w.positionGroupBAddress);
            j.at("horizontalPositionSize").get_to(w.horizontalPositionSize);
            j.at("positionGroups").get_to(w.positionGroups);
        }
    };

    template <>
    struct adl_serializer<PlcClientCppAdapter::Configure::Workstation3D::Position> {
        static void from_json(const json& j, PlcClientCppAdapter::Configure::Workstation3D::Position& p) {
            j.at("zAxisMask").get_to(p.zAxisMask);

            auto& zCoords = j.at("zAxisCoordinates");
            for (size_t i = 0; i < zCoords.size() && i < 16; ++i)
                p.zAxisCoordinates[i] = zCoords[i].get<float>();
        }
    };

    template <>
    struct adl_serializer<PlcClientCppAdapter::Configure::Workstation3D> {
        static void from_json(const json& j, PlcClientCppAdapter::Configure::Workstation3D& w) {
            j.at("modbusChannelIndex").get_to(w.modbusChannelIndex);
            j.at("workstationControlAAddress").get_to(w.workstationControlAAddress);
            j.at("workstationControlBAddress").get_to(w.workstationControlBAddress);
            j.at("positionBAddress").get_to(w.positionBAddress);
            j.at("horizontalPositionSize").get_to(w.horizontalPositionSize);
            j.at("positions").get_to(w.positions);
        }
    };

    template <>
    struct adl_serializer<PlcClientCppAdapter::Configure::WorkstationUnload> {
        static void from_json(const json& j, PlcClientCppAdapter::Configure::WorkstationUnload& w) {
            j.at("modbusChannelIndex").get_to(w.modbusChannelIndex);
            j.at("workstationControlAAddress").get_to(w.workstationControlAAddress);
            j.at("workstationControlBAddress").get_to(w.workstationControlBAddress);
            j.at("sortResultAAddress").get_to(w.sortResultAAddress);
            j.at("resultBAddress").get_to(w.resultBAddress);
        }
    };

    template <>
    struct adl_serializer<PlcClientCppAdapter::Configure::Alarm> {
        static void from_json(const json& j, PlcClientCppAdapter::Configure::Alarm& a) {
            j.at("modbusChannelIndex").get_to(a.modbusChannelIndex);
            j.at("alarmInfoAAddress").get_to(a.alarmInfoAAddress);
        }
    };

    template <>
    struct adl_serializer<PlcClientCppAdapter::Configure> {
        static void from_json(const json& j, PlcClientCppAdapter::Configure& c) {
            j.at("modbusChannels").get_to(c.modbusChannels);
            j.at("system").get_to(c.system);
            j.at("workstationLaserRangings").get_to(c.workstationLaserRangings);
            j.at("workstation2Ds").get_to(c.workstation2Ds);
            j.at("workstation3Ds").get_to(c.workstation3Ds);
            j.at("workstationUnloads").get_to(c.workstationUnloads);
            j.at("alarm").get_to(c.alarm);
        }
    };
}
