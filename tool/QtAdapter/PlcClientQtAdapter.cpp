#include "PlcClientQtAdapter.h"
#include "PlcClient.h"

#include <sstream>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

#include "magic_enum.hpp"

PlcClientQtAdapter& PlcClientQtAdapter::getInstance() {
    static PlcClientQtAdapter instance;

    static std::once_flag initialized;
    std::call_once(initialized, []
    {
        plcclient_setLogCallback([](const PlcClientLogType logType, const char *log) {
            emit instance.log(static_cast<LogType>(logType), log);
        });

        plcclient_setModbusConnectionStateChangedCallback([](const size_t modbusChannelIndex, const bool connected) {
            emit instance.modbusConnectionStateChanged(static_cast<qsizetype>(modbusChannelIndex), connected);
        });

        plcclient_setRunningStateChangedCallback([](const PlcClientRunningState runningState) {
            emit instance.runningStateChanged(static_cast<RunningState>(runningState));
        });

        plcclient_setStateWordChangedCallback([](const uint16_t stateWord) {
            emit instance.stateWordChanged(stateWord);
        });

        plcclient_setAlarmStateChangedCallback([](const bool *alarmState) {
            std::array<bool, 160> alarms{};
            std::copy_n(alarmState, alarms.size(), alarms.begin());
            emit instance.alarmStateChanged(alarms);
        });

        plcclient_setWorkstationStarted2DCallback([](const size_t workstation2DIndex, const char *trayCode) {
            emit instance.workstationStarted2D(static_cast<qsizetype>(workstation2DIndex), trayCode);
        });

        plcclient_setPositionGroupStarted2DCallback([](const size_t workstation2DIndex, const size_t horizontalPositionIndex, const size_t positionGroupIndex) {
            emit instance.positionGroupStarted2D(static_cast<qsizetype>(workstation2DIndex), static_cast<qsizetype>(horizontalPositionIndex), static_cast<qsizetype>(positionGroupIndex));
        });

        plcclient_setQueryPositionGroupResult2DCallback([](const size_t workstation2DIndex, const size_t horizontalPositionIndex, const size_t positionGroupIndex, bool &result) {
            emit instance.queryPositionGroupResult2D(static_cast<qsizetype>(workstation2DIndex), static_cast<qsizetype>(horizontalPositionIndex), static_cast<qsizetype>(positionGroupIndex), result);
        });

        plcclient_setWorkstationFinished2DCallback([](const size_t workstation2DIndex) {
            emit instance.workstationFinished2D(static_cast<qsizetype>(workstation2DIndex));
        });

        plcclient_setWorkstationStarted3DCallback([](const size_t workstation3DIndex, const char *trayCode) {
            emit instance.workstationStarted3D(static_cast<qsizetype>(workstation3DIndex), trayCode);
        });

        plcclient_setPositionStarted3DCallback([](const size_t workstation3DIndex, ptrdiff_t &horizontalPositionIndex, size_t &positionIndex) {
            auto qPositionIndex = static_cast<qsizetype>(positionIndex);
            emit instance.positionStarted3D(static_cast<qsizetype>(workstation3DIndex), horizontalPositionIndex, qPositionIndex);
            positionIndex = static_cast<size_t>(qPositionIndex);
        });

        plcclient_setPositionArrived3DCallback([](const size_t workstation3DIndex, const size_t horizontalPositionIndex, const size_t positionIndex) {
            emit instance.positionArrived3D(static_cast<qsizetype>(workstation3DIndex), static_cast<qsizetype>(horizontalPositionIndex), static_cast<qsizetype>(positionIndex));
        });

        plcclient_setWorkstationFinished3DCallback([](const size_t workstation3DIndex) {
            emit instance.workstationFinished3D(static_cast<qsizetype>(workstation3DIndex));
        });

        plcclient_setWorkstationUnloadQueryResultCallback([](const size_t workstationUnloadIndex, const char *trayCode, uint8_t *result) {
            std::array<quint8, 400> r{};
            emit instance.queryResult(static_cast<qsizetype>(workstationUnloadIndex), trayCode, r);
            std::copy(r.begin(), r.end(), result);
        });

        plcclient_setWorkstationUnloadSortResultCallback([](const size_t workstationUnloadIndex, const char *trayCode, const ::SortResult *sortResult, const size_t sortResultSize, bool &result) {
            QVector<SortResult> sortResults {};
            for (size_t i{};i<sortResultSize;i++) {
                sortResults.append({sortResult[i].trayType, sortResult[i].trayIndex, sortResult[i].holeIndex});
            }
            emit instance.sortResult(static_cast<qsizetype>(workstationUnloadIndex), trayCode, sortResults, result);
        });
    });

    return instance;
}

bool PlcClientQtAdapter::start(const Configure &configure) {

    emit log(LogType::INFO, QString("开始启动。配置信息：\n") + QString::fromStdString(configure.toString()));

    ::PlcClientConfigure plcClientConfigure {};

    // Modbus 连接
    struct ModbusChannelData {
        QByteArray ip;
    };
    QVector<ModbusChannelData> modbusChannelDatas;
    QVector<::PlcClientConfigure::ModbusChannel> modbusChannels;

    modbusChannelDatas.reserve(configure.modbusChannels.size());
    modbusChannels.reserve(configure.modbusChannels.size());

    for (const auto &[
        ip,
        port
        ] : configure.modbusChannels) {
        modbusChannelDatas.append({ip.toUtf8()});
        modbusChannels.append({modbusChannelDatas.last().ip.constData(), port});
    }
    plcClientConfigure.modbusChannels = modbusChannels.constData();
    plcClientConfigure.modbusChannelsSize = modbusChannels.size();

    // 系统
    struct System {
        QByteArray productCode;
    } system {
        configure.system.productCode.toUtf8()
    };

    plcClientConfigure.system = {
        static_cast<size_t>(configure.system.modbusChannelIndex),
        configure.system.systemAAddress,
        configure.system.systemBAddress,
        system.productCode.constData()
    };

    // 激光测距工位
    struct WorkstationLaserRangingData {
    };
    QVector<WorkstationLaserRangingData> workstationLaserRangingDatas;
    QVector<::PlcClientConfigure::WorkstationLaserRanging> workstationLaserRangings;

    workstationLaserRangingDatas.reserve(configure.workstationLaserRangings.size());
    workstationLaserRangings.reserve(configure.workstationLaserRangings.size());

    for (const auto &[
        modbusChannelIndex,
        workstationControlAAddress,
        workstationControlBAddress,
        statusAAddress,
        positionBAddress,
        horizontalPositionSize,
        referenceValue,
        lowerLimitValue,
        upperLimitValue,
        sensorSize
        ] : configure.workstationLaserRangings) {
        workstationLaserRangings.append({
            static_cast<size_t>(modbusChannelIndex),
            workstationControlAAddress,
            workstationControlBAddress,
            statusAAddress,
            positionBAddress,
            horizontalPositionSize,
            referenceValue,
            lowerLimitValue,
            upperLimitValue,
            static_cast<PlcClientConfigure::WorkstationLaserRanging::SensorSize>(sensorSize),
        });
    }
    plcClientConfigure.workstationLaserRangings = workstationLaserRangings.constData();
    plcClientConfigure.workstationLaserRangingsSize = workstationLaserRangings.size();

    // 2D工位
    struct Workstation2DData {
        struct PositionGroupData {
            QVector<::PlcClientConfigure::Workstation2D::PositionGroup::Position> positions;
        };
        QVector<PositionGroupData> positionGroupDatas;
        QVector<::PlcClientConfigure::Workstation2D::PositionGroup> positionGroups;
    };
    QVector<Workstation2DData> workstation2DDatas;
    QVector<::PlcClientConfigure::Workstation2D> workstation2Ds;

    workstation2DDatas.reserve(configure.workstation2Ds.size());
    workstation2Ds.reserve(configure.workstation2Ds.size());

    for (const auto &[
        modbusChannelIndex,
        workstationControlAAddress,
        workstationControlBAddress,
        positionGroupBAddress,
        horizontalPositionSize,
        positionGroups
        ] : configure.workstation2Ds) {
        workstation2DDatas.append(Workstation2DData{});
        for (const auto &[
            lightMask,
            lightCoordinates,
            positions
            ] : positionGroups) {
            workstation2DDatas.last().positionGroupDatas.append(Workstation2DData::PositionGroupData{});
            for (const auto &[
                holdDuration,
                cameraMask,
                cameraCoordinates,
                cameraChannelTypes
                ]: positions) {
                workstation2DDatas.last().positionGroupDatas.last().positions.append({
                    holdDuration,
                    cameraMask,
                    {},
                    {}
                });
                std::copy(cameraCoordinates.begin(),
                          cameraCoordinates.end(),
                          workstation2DDatas.last().positionGroupDatas.last().positions.last().cameraCoordinates
                );
                std::transform(cameraChannelTypes.begin(), cameraChannelTypes.end(), workstation2DDatas.last().positionGroupDatas.last().positions.last().cameraChannelTypes, [](Configure::Workstation2D::PositionGroup::Position::CameraChannelType cameraChannelType)
                {
                    return static_cast<PlcClientConfigure::Workstation2D::PositionGroup::Position::CameraChannelType>(cameraChannelType);
                });
            }
            workstation2DDatas.last().positionGroups.append({
                lightMask,
                {},
                workstation2DDatas.last().positionGroupDatas.last().positions.constData(),
                static_cast<size_t>(workstation2DDatas.last().positionGroupDatas.last().positions.size())
            });
            std::copy(lightCoordinates.begin(),
                      lightCoordinates.end(),
                      workstation2DDatas.last().positionGroups.last().lightCoordinates
            );
        }

        workstation2Ds.append({
            static_cast<size_t>(modbusChannelIndex),
            workstationControlAAddress,
            workstationControlBAddress,
            positionGroupBAddress,
            horizontalPositionSize,
            workstation2DDatas.last().positionGroups.constData(),
            static_cast<size_t>(workstation2DDatas.last().positionGroupDatas.size())
        });
    }
    plcClientConfigure.workstation2Ds = workstation2Ds.constData();
    plcClientConfigure.workstation2DsSize = workstation2Ds.size();

    // 3D工位
    struct Workstation3DData {
        QVector<::PlcClientConfigure::Workstation3D::Position> positions;
    };
    QVector<Workstation3DData> workstation3DDatas;
    QVector<::PlcClientConfigure::Workstation3D> workstation3Ds;

    workstation3DDatas.reserve(configure.workstation3Ds.size());
    workstation3Ds.reserve(configure.workstation3Ds.size());

    for (const auto &[
        modbusChannelIndex,
        workstationControlAAddress,
        workstationControlBAddress,
        positionBAddress,
        horizontalPositionSize,
        positions
        ] : configure.workstation3Ds) {
        workstation3DDatas.append(Workstation3DData{});
        for (const auto &[
            zAxisMask,
            zAxisCoordinates
            ] : positions) {
            workstation3DDatas.last().positions.append(::PlcClientConfigure::Workstation3D::Position {
                zAxisMask,
                {}
            });
            std::copy(zAxisCoordinates.begin(),
                      zAxisCoordinates.end(),
                      workstation3DDatas.last().positions.last().zAxisCoordinates
            );
        }
        workstation3Ds.append({
            static_cast<size_t>(modbusChannelIndex),
            workstationControlAAddress,
            workstationControlBAddress,
            positionBAddress,
            horizontalPositionSize,
            workstation3DDatas.last().positions.constData(),
            static_cast<size_t>(workstation3DDatas.last().positions.size())
        });
    }
    plcClientConfigure.workstation3Ds = workstation3Ds.constData();
    plcClientConfigure.workstation3DsSize = workstation3Ds.size();

    // 下料位
    struct WorkstationUnloadData {
    };
    QVector<WorkstationUnloadData> workstationUnloadDatas;
    QVector<::PlcClientConfigure::WorkstationUnload> workstationUnloads;

    workstationUnloadDatas.reserve(configure.workstationUnloads.size());
    workstationUnloads.reserve(configure.workstationUnloads.size());

    for (const auto &[
        modbusChannelIndex,
        workstationControlAAddress,
        workstationControlBAddress,
        sortResultAAddress,
        resultBAddress
        ] : configure.workstationUnloads) {
        workstationUnloads.append({
            static_cast<size_t>(modbusChannelIndex),
            workstationControlAAddress,
            workstationControlBAddress,
            sortResultAAddress,
            resultBAddress
        });
    }
    plcClientConfigure.workstationUnloads = workstationUnloads.constData();
    plcClientConfigure.workstationUnloadsSize = workstationUnloads.size();

    // 报警
    struct Alarm {
    } alarm;

    plcClientConfigure.alarm = {
        static_cast<size_t>(configure.alarm.modbusChannelIndex),
        configure.alarm.alarmInfoAAddress
    };

    return plcclient_start(plcClientConfigure);
}

void PlcClientQtAdapter::stop() {
    plcclient_stop();
}

QString PlcClientQtAdapter::getVersion() {
    return {plcclient_getVersion()};
}

PlcClientQtAdapter::Configure PlcClientQtAdapter::Configure::fromJson(const QString& fileName) {
    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly)) {
        throw std::runtime_error("打开文件失败:" + fileName.toStdString());
    }

    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &parseError);
    if (doc.isNull()) {
        throw std::runtime_error(parseError.errorString().toStdString());
    }

    if (!doc.isObject()) {
        throw std::runtime_error("根元素不是对象");
    }

    QJsonObject root = doc.object();
    Configure config;

    // ModbusChannels
    QJsonArray modbusChannels = root["modbusChannels"].toArray();
    for (const auto& val : modbusChannels) {
        QJsonObject obj = val.toObject();
        ModbusChannel channel;
        channel.ip = obj["ip"].toString();
        channel.port = obj["port"].toInt();
        config.modbusChannels.append(channel);
    }

    // System
    QJsonObject system = root["system"].toObject();
    config.system.modbusChannelIndex = system["modbusChannelIndex"].toInt();
    config.system.systemAAddress = system["systemAAddress"].toInt();
    config.system.systemBAddress = system["systemBAddress"].toInt();
    config.system.productCode = system["productCode"].toString();

    // WorkstationLaserRangings
    QJsonArray laserRangings = root["workstationLaserRangings"].toArray();
    for (const auto& val : laserRangings) {
        QJsonObject obj = val.toObject();
        WorkstationLaserRanging wlr;
        wlr.modbusChannelIndex = obj["modbusChannelIndex"].toInt();
        wlr.workstationControlAAddress = obj["workstationControlAAddress"].toInt();
        wlr.workstationControlBAddress = obj["workstationControlBAddress"].toInt();
        wlr.statusAAddress = obj["statusAAddress"].toInt();
        wlr.positionBAddress = obj["positionBAddress"].toInt();
        wlr.horizontalPositionSize = obj["horizontalPositionSize"].toInt();
        wlr.referenceValue = static_cast<float>(obj["referenceValue"].toDouble());
        wlr.lowerLimitValue = static_cast<float>(obj["lowerLimitValue"].toDouble());
        wlr.upperLimitValue = static_cast<float>(obj["upperLimitValue"].toDouble());
        int sensorSize = obj["sensorSize"].toInt();
        wlr.sensorSize = (sensorSize == 1) ? WorkstationLaserRanging::SensorSize::ONE : WorkstationLaserRanging::SensorSize::TWO;
        config.workstationLaserRangings.append(wlr);
    }

    // Workstation2Ds
    QJsonArray workstation2Ds = root["workstation2Ds"].toArray();
    for (const auto& w2dVal : workstation2Ds) {
        QJsonObject w2dObj = w2dVal.toObject();
        Workstation2D w2d;
        w2d.modbusChannelIndex = w2dObj["modbusChannelIndex"].toInt();
        w2d.workstationControlAAddress = w2dObj["workstationControlAAddress"].toInt();
        w2d.workstationControlBAddress = w2dObj["workstationControlBAddress"].toInt();
        w2d.positionGroupBAddress = w2dObj["positionGroupBAddress"].toInt();
        w2d.horizontalPositionSize = w2dObj["horizontalPositionSize"].toInt();

        QJsonArray positionGroups = w2dObj["positionGroups"].toArray();
        for (const auto& pgVal : positionGroups) {
            QJsonObject pgObj = pgVal.toObject();
            Workstation2D::PositionGroup pg;
            pg.lightMask = pgObj["lightMask"].toInt();
            pg.lightCoordinates = jsonArrayToFloatArray<16>(pgObj["lightCoordinates"].toArray());

            QJsonArray positions = pgObj["positions"].toArray();
            for (const auto& posVal : positions) {
                QJsonObject posObj = posVal.toObject();
                Workstation2D::PositionGroup::Position pos;
                pos.holdDuration = posObj["holdDuration"].toInt();
                pos.cameraMask = posObj["cameraMask"].toInt();
                pos.cameraCoordinates = jsonArrayToFloatArray<16>(posObj["cameraCoordinates"].toArray());

                QJsonArray channelTypes = posObj["cameraChannelTypes"].toArray();
                for (int i = 0; i < 16; ++i) {
                    int type = (i < channelTypes.size()) ? channelTypes[i].toInt() : 0;
                    pos.cameraChannelTypes[i] = static_cast<Workstation2D::PositionGroup::Position::CameraChannelType>(type);
                }

                /*
                QJsonArray imageNumbers = posObj["imageNumber"].toArray();
                for (int i = 0; i < 16; ++i) {
                    pos.imageNumber[i] = (i < imageNumbers.size()) ? imageNumbers[i].toInt() : 0;
                }
                */

                pg.positions.append(pos);
            }
            w2d.positionGroups.append(pg);
        }
        config.workstation2Ds.append(w2d);
    }

    // Workstation3Ds
    QJsonArray workstation3Ds = root["workstation3Ds"].toArray();
    for (const auto& w3dVal : workstation3Ds) {
        QJsonObject w3dObj = w3dVal.toObject();
        Workstation3D w3d;
        w3d.modbusChannelIndex = w3dObj["modbusChannelIndex"].toInt();
        w3d.workstationControlAAddress = w3dObj["workstationControlAAddress"].toInt();
        w3d.workstationControlBAddress = w3dObj["workstationControlBAddress"].toInt();
        w3d.positionBAddress = w3dObj["positionBAddress"].toInt();
        w3d.horizontalPositionSize = w3dObj["horizontalPositionSize"].toInt();

        QJsonArray positions = w3dObj["positions"].toArray();
        for (const auto& posVal : positions) {
            QJsonObject posObj = posVal.toObject();
            Workstation3D::Position pos;
            pos.zAxisMask = posObj["zAxisMask"].toInt();
            pos.zAxisCoordinates = jsonArrayToFloatArray<16>(posObj["zAxisCoordinates"].toArray());
            w3d.positions.append(pos);
        }
        config.workstation3Ds.append(w3d);
    }

    // WorkstationUnloads
    QJsonArray workstationUnloads = root["workstationUnloads"].toArray();
    for (const auto& val : workstationUnloads) {
        QJsonObject obj = val.toObject();
        WorkstationUnload wu;
        wu.modbusChannelIndex = obj["modbusChannelIndex"].toInt();
        wu.workstationControlAAddress = obj["workstationControlAAddress"].toInt();
        wu.workstationControlBAddress = obj["workstationControlBAddress"].toInt();
        wu.sortResultAAddress = obj["sortResultAAddress"].toInt();
        wu.resultBAddress = obj["resultBAddress"].toInt();
        config.workstationUnloads.append(wu);
    }

    // Alarm
    QJsonObject alarm = root["alarm"].toObject();
    config.alarm.modbusChannelIndex = alarm["modbusChannelIndex"].toInt();
    config.alarm.alarmInfoAAddress = alarm["alarmInfoAAddress"].toInt();

    return config;
}

std::string PlcClientQtAdapter::Configure::toString() const {
    std::ostringstream oss;

    oss << "Modbus Channels:" << std::endl;
    for (int i=0;i<modbusChannels.size();i++) {
        oss << "    " << i << ": " << modbusChannels.at(i).ip.toStdString() << ":" << modbusChannels.at(i).port << std::endl;
    }
    oss << std::endl;

    oss << "System:" << std::endl;
    oss << "    " << "ModbusChannelIndex: " << system.modbusChannelIndex << std::endl;
    oss << "    " << "SystemAAddress: " << system.systemAAddress << std::endl;
    oss << "    " << "SystemBAddress: " << system.systemBAddress << std::endl;
    oss << "    " << "ProductCode: " << system.productCode.toStdString() << std::endl;
    oss << std::endl;

    for (int i=0;i<workstationLaserRangings.size();i++) {
        oss << "WorkstationLaserRanging " << i+1 << ":" << std::endl;
        oss << "    " << "ModbusChannelIndex: " << workstationLaserRangings.at(i).modbusChannelIndex << std::endl;
        oss << "    " << "WorkstationControlAAddress: " << workstationLaserRangings.at(i).workstationControlAAddress << std::endl;
        oss << "    " << "WorkstationControlBAddress: " << workstationLaserRangings.at(i).workstationControlBAddress << std::endl;
        oss << "    " << "StatusAAddress: " << workstationLaserRangings.at(i).statusAAddress << std::endl;
        oss << "    " << "PositionBAddress: " << workstationLaserRangings.at(i).positionBAddress << std::endl;
        oss << "    " << "HorizontalPositionSize: " << workstationLaserRangings.at(i).horizontalPositionSize << std::endl;
        oss << "    " << "ReferenceValue: " << workstationLaserRangings.at(i).referenceValue << std::endl;
        oss << "    " << "LowerLimit: " << workstationLaserRangings.at(i).lowerLimitValue << std::endl;
        oss << "    " << "UpperLimit: " << workstationLaserRangings.at(i).upperLimitValue << std::endl;
        oss << "    " << "SensorSize: " << magic_enum::enum_name(workstationLaserRangings.at(i).sensorSize) << std::endl;
    }
    oss << std::endl;

    for (int i=0;i<workstation2Ds.size();i++) {
        oss << "Workstation2D " << i+1 << ":" << std::endl;
        oss << "    " << "ModbusChannelIndex: " << workstation2Ds.at(i).modbusChannelIndex << std::endl;
        oss << "    " << "WorkstationControlAAddress: " << workstation2Ds.at(i).workstationControlAAddress << std::endl;
        oss << "    " << "WorkstationControlBAddress: " << workstation2Ds.at(i).workstationControlBAddress << std::endl;
        oss << "    " << "PositionGroupBAddress: " << workstation2Ds.at(i).positionGroupBAddress << std::endl;
        oss << "    " << "HorizontalPositionSize: " << workstation2Ds.at(i).horizontalPositionSize << std::endl;
        oss << "    " << "PositionGroup: " << std::endl;
        for (int j=0;j<workstation2Ds.at(i).positionGroups.size();j++) {
            oss << "    " << "    " << std::setw(2) << j << ": " << "LightMask: " << workstation2Ds.at(i).positionGroups.at(j).lightMask << std::endl;
            oss << "    " << "    " << "    " << "LightCoordinate: ";
            oss << "[";
            for (size_t k=0; k<workstation2Ds.at(i).positionGroups.at(j).lightCoordinates.size(); k++) {
                oss << workstation2Ds.at(i).positionGroups.at(j).lightCoordinates[k];
                if (k != workstation2Ds.at(i).positionGroups.at(j).lightCoordinates.size() - 1) {
                    oss << ", ";
                }
            }
            oss << "]" << std::endl;
            oss << "    " << "    " << "    " << "Position: " << std::endl;
            for (int k=0;k<workstation2Ds.at(i).positionGroups.at(j).positions.size();k++) {
                oss << "    " << "    " << "    " << "    " << std::setw(2) << k << ": " << std::endl;
                oss << "    " << "    " << "    " << "    " << "    " << "HoldDuration: " << workstation2Ds.at(i).positionGroups.at(j).positions.at(k).holdDuration << std::endl;
                oss << "    " << "    " << "    " << "    " << "    " << "CameraMask: " << workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraMask << std::endl;
                oss << "    " << "    " << "    " << "    " << "    " << "CameraCoordinate: ";
                oss << "[";
                for (size_t l=0; l<workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraCoordinates.size(); l++) {
                    oss << workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraCoordinates[l];
                    if (k != workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraCoordinates.size() - 1) {
                        oss << ", ";
                    }
                }
                oss << "]" << std::endl;
                oss << "    " << "    " << "    " << "    " << "    " << "CameraChannelTypes: ";
                oss << "[";
                for (size_t l=0; l<workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraChannelTypes.size(); l++) {
                    oss << magic_enum::enum_name(workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraChannelTypes[l]);
                    if (k != workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraChannelTypes.size() - 1) {
                        oss << ", ";
                    }
                }
                oss << "]" << std::endl;
                /*
                oss << "    " << "    " << "    " << "    " << "    " << "CameraImageNumbers: ";
                oss << "[";
                for (size_t l=0; l<workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraImageNumbers.size(); l++) {
                    oss << workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraImageNumbers[l];
                    if (k != workstation2Ds.at(i).positionGroups.at(j).positions.at(k).cameraImageNumbers.size() - 1) {
                        oss << ", ";
                    }
                }
                oss << "]" << std::endl;
                */
            }
        }
    }
    oss << std::endl;

    for (int i=0;i<workstation3Ds.size();i++) {
        oss << "Workstation3D " << i+1 << ":" << std::endl;
        oss << "    " << "ModbusChannelIndex: " << workstation3Ds.at(i).modbusChannelIndex << std::endl;
        oss << "    " << "WorkstationControlAAddress: " << workstation3Ds.at(i).workstationControlAAddress << std::endl;
        oss << "    " << "WorkstationControlBAddress: " << workstation3Ds.at(i).workstationControlBAddress << std::endl;
        oss << "    " << "PositionBAddress: " << workstation3Ds.at(i).positionBAddress << std::endl;
        oss << "    " << "HorizontalPositionSize: " << workstation3Ds.at(i).horizontalPositionSize << std::endl;
        oss << "    " << "Position: " << std::endl;
        for (int j=0;j<workstation3Ds.at(i).positions.size();j++) {
            oss << "    " << "    " << std::setw(2) << j << ": " << "ZAxisMask: " << workstation3Ds.at(i).positions.at(j).zAxisMask << std::endl;
            oss << "    " << "    " << "    " << "ZAxisCoordinate: ";
            oss << "[";
            for (size_t k=0; k<workstation3Ds.at(i).positions.at(j).zAxisCoordinates.size(); k++) {
                oss << workstation3Ds.at(i).positions.at(j).zAxisCoordinates[k];
                if (k != workstation3Ds.at(i).positions.at(j).zAxisCoordinates.size() - 1) {
                    oss << ", ";
                }
            }
            oss << "]" << std::endl;
        }
    }
    oss << std::endl;

    for (int i=0;i<workstationUnloads.size();i++) {
        oss << "WorkstationUnloads " << i+1 << ":" << std::endl;
        oss << "    " << "ModbusChannelIndex: " << workstationUnloads.at(i).modbusChannelIndex << std::endl;
        oss << "    " << "WorkstationControlAAddress: " << workstationUnloads.at(i).workstationControlAAddress << std::endl;
        oss << "    " << "WorkstationControlBAddress: " << workstationUnloads.at(i).workstationControlBAddress << std::endl;
        oss << "    " << "SortResultAAddress: " << workstationUnloads.at(i).sortResultAAddress << std::endl;
        oss << "    " << "ResultBAddress: " << workstationUnloads.at(i).resultBAddress << std::endl;
    }
    oss << std::endl;

    oss << "Alarm:" << std::endl;
    oss << "    " << "ModbusChannelIndex: " << alarm.modbusChannelIndex << std::endl;
    oss << "    " << "AlarmInfoAAddress: " << alarm.alarmInfoAAddress << std::endl;
    oss << std::endl;

    return oss.str();
}

template <size_t N>
std::array<float, N> PlcClientQtAdapter::Configure::jsonArrayToFloatArray(const QJsonArray& jsonArray) {
    std::array<float, N> arr{};
    for (size_t i = 0; i < N; ++i) {
        if (i < static_cast<size_t>(jsonArray.size())) {
            arr[i] = static_cast<float>(jsonArray[static_cast<qsizetype>(i)].toDouble());
        } else {
            arr[i] = 0.0f;
        }
    }
    return arr;
}
