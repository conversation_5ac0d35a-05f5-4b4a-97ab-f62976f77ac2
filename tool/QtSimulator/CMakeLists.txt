cmake_minimum_required(VERSION 3.28)
project(QtSimulator)

set(CMAKE_CXX_STANDARD 17)

set(CMAKE_AUTOMOC TRUE)

find_package(Qt6 COMPONENTS Core CONFIG REQUIRED)

set(TARGET ${PROJECT_NAME})
add_executable(${TARGET}
        src/main.cpp
        ../QtAdapter/PlcClientQtAdapter.cpp
        ../QtAdapter/PlcClientQtAdapter.h
)

target_include_directories(${TARGET} PRIVATE ../QtAdapter)
target_link_libraries(${TARGET} PRIVATE Qt6::Core)
target_link_libraries(${TARGET} PRIVATE PlcClient-static)

if (MSVC)
    target_compile_options(${TARGET} PRIVATE "/utf-8")
endif ()

if (CMAKE_BUILD_TYPE STREQUAL "Release" OR CMAKE_BUILD_TYPE STREQUAL "RelWithDebInfo")
    install(TARGETS ${TARGET} RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/tool/${TARGET})
    #get_target_property(Qt6Core.dll Qt6::Core LOCATION)
    #install(FILES ${Qt6Core.dll} DESTINATION ${CMAKE_INSTALL_PREFIX}/tool/${TARGET})
endif ()
