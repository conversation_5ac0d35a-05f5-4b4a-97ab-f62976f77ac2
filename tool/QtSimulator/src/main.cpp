#pragma warning(disable : 4834)

#include <QMutex>
#include <QCoreApplication>
#include <QRandomGenerator>
#include <QThread>
#include <QDir>

#include <iostream>

#include <Windows.h>

#include "PlcClientQtAdapter.h"
#include "magic_enum.hpp"

void printLog(const QString& content) {
    static QMutex cOutMutex;

    QMutexLocker lock(&cOutMutex);
    std::cout << QString(QDateTime::currentDateTime().toString("[yyyy-MM-dd HH:mm:ss.zzz]") + " [main.cpp] " + content).toStdString() << std::endl;
}

int main(int argc, char* argv[]) {
    SetConsoleOutputCP(CP_UTF8);
    printLog(QString("PlcClient Version: %1").arg(PlcClientQtAdapter::getInstance().getVersion()));

    QCoreApplication app(argc, argv);

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::log, [](const PlcClientQtAdapter::LogType &logType, const QString &log) {
        if (logType != PlcClientQtAdapter::LogType::DEBUG) {
            printLog(log);
        }
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::modbusConnectionStateChanged, [](const qsizetype modbusChannelIndex, const bool connected) {
        printLog(QString("第%1个Modbus连接的状态改变:%2").arg(modbusChannelIndex).arg(connected));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::runningStateChanged, [](const PlcClientQtAdapter::RunningState &runningState) {
        printLog(QString("运行状态改变:%1").arg(magic_enum::enum_name(runningState).data()));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::stateWordChanged, [](const quint16 stateWord) {
        printLog(QString("状态字改变:%1").arg(stateWord));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::alarmStateChanged, [](const std::array<bool, 160> &alarms) {
        QString s;
        QTextStream ts(&s);
        for (std::size_t i=0;i<alarms.size();i++) {
            if (i%16 == 0) {
                ts << "  " << Qt::endl << QString("[%1-%2] ").arg(i, 3).arg(i+16, 3);
            }
            ts << QString::number(alarms[i]);
        }
        printLog(QString("报警状态改变：%1").arg(s));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::workstationStarted2D, [](const qsizetype workstation2DIndex, const QString &trayCode) {
        printLog(QString("2D工位%1 工位开始 托盘号:%2").arg(workstation2DIndex+1).arg(trayCode));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::positionGroupStarted2D, [](const qsizetype workstation2DIndex, const qsizetype horizontalPositionIndex, const qsizetype positionGroupIndex) {
        printLog(QString("2D工位%1 水平点位%2 点位组%3 开始").arg(workstation2DIndex+1).arg(horizontalPositionIndex).arg(positionGroupIndex));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::queryPositionGroupResult2D, [](const qsizetype workstation2DIndex, const qsizetype horizontalPositionIndex, const qsizetype positionGroupIndex, bool &result) {
        result = true;
        printLog(QString("2D工位%1 水平点位%2 点位组%3 查询结果:%4").arg(workstation2DIndex+1).arg(horizontalPositionIndex).arg(positionGroupIndex).arg(result));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::workstationFinished2D, [](const qsizetype workstation2DIndex) {
        printLog(QString("2D工位%1 工位结束").arg(workstation2DIndex+1));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::workstationStarted3D, [](const qsizetype workstation3DIndex, const QString &trayCode) {
        printLog(QString("3D工位%1 工位开始 托盘号:%2").arg(workstation3DIndex+1).arg(trayCode));
    });

    qsizetype currentHorizontalPositionIndex3D = 0;
    qsizetype horizontalPositionCount = 0;
    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::positionStarted3D, [&currentHorizontalPositionIndex3D, &horizontalPositionCount](const qsizetype workstation3DIndex, qsizetype &horizontalPositionIndex, qsizetype &positionIndex) {
        if (currentHorizontalPositionIndex3D < horizontalPositionCount) {
            horizontalPositionIndex = currentHorizontalPositionIndex3D++;
            positionIndex = 0;
        } else {
            currentHorizontalPositionIndex3D = 0;
            horizontalPositionIndex =-1;
            positionIndex = 0;
        }
        printLog(QString("3D工位%1 水平点位%2 点位%3 开始").arg(workstation3DIndex+1).arg(horizontalPositionIndex).arg(positionIndex));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::positionArrived3D, [](const qsizetype workstation3DIndex, const qsizetype horizontalPositionIndex, const qsizetype positionIndex) {
        printLog(QString("3D工位%1 水平点位%2 点位%3 已到达").arg(workstation3DIndex+1).arg(horizontalPositionIndex).arg(positionIndex));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::workstationFinished3D, [](const qsizetype workstation3DIndex) {
        printLog(QString("3D工位%1 工位结束").arg(workstation3DIndex+1));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::queryResult, [](const qsizetype workstationUnloadIndex, const QString &trayCode, std::array<quint8, 400> &result) {
        QString s;
        QTextStream ts(&s);

        ts << Qt::endl;
        for (unsigned char & i : result) {
            i = QRandomGenerator::global()->bounded(3)+1;
            ts << i;
        }

        printLog(QString("下料工位%1 托盘号:%2 查询结果:%3").arg(workstationUnloadIndex+1).arg(trayCode).arg(s));
    });

    QObject::connect(&PlcClientQtAdapter::getInstance(), &PlcClientQtAdapter::sortResult, [](const qsizetype workstationUnloadIndex, const QString &trayCode, QVector<PlcClientQtAdapter::SortResult> &sortResults, bool &result) {
        result = true;

        QString s;
        QTextStream ts(&s);

        ts << Qt::endl;
        for (const auto & [trayType, trayIndex, holeIndex] : sortResults) {
            ts << "托盘类型:" << trayType << " 托盘编号:" << trayIndex << " 穴位编号:" << holeIndex << Qt::endl;
        }

        printLog(QString("下料工位%1 托盘号:%2 分拣结果:%3").arg(workstationUnloadIndex+1).arg(trayCode).arg(s));
    });

    try {
        const auto configure = PlcClientQtAdapter::Configure::fromJson(QCoreApplication::applicationDirPath() + QDir::separator() + "PlcClientConfigure.json");

        printLog("读取 PlcClientConfigure.json 成功");

        if (!configure.workstation3Ds.empty()) {
            horizontalPositionCount = configure.workstation3Ds.at(0).horizontalPositionSize;
        }

        if (!PlcClientQtAdapter::getInstance().start(configure)) {
            printLog("启动失败");
            return -1;
        }

        while (true) {
            QThread::sleep(10);
        }

        PlcClientQtAdapter::getInstance().stop();

    } catch (const std::exception& e) {
        printLog(QString("读取 PlcClientConfigure.json 失败：") + e.what());
        return -2;
    }

    return 0;
}
