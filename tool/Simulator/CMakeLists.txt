cmake_minimum_required(VERSION 3.28)
project(Simulator)

set(CMAKE_CXX_STANDARD 20)

set(TARGET ${PROJECT_NAME})
add_executable(${TARGET}
        src/main.cpp
        ../CppAdapter/PlcClientCppAdapter.cpp
        ../CppAdapter/PlcClientCppAdapter.h
)

target_include_directories(${TARGET} PRIVATE ../CppAdapter)
target_link_libraries(${TARGET} PRIVATE PlcClient-static)

if (MSVC)
    target_compile_options(${TARGET} PRIVATE "/utf-8")
endif ()

if (CMAKE_BUILD_TYPE STREQUAL "Release" OR CMAKE_BUILD_TYPE STREQUAL "RelWithDebInfo")
    install(TARGETS ${TARGET} RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/tool/${TARGET})
endif ()
