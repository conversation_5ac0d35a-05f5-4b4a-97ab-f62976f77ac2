Local 用于OCT标机项目的PLC通信部分。

Build step:

1. Install `libmodbus` package.
2. Execute below command in the directory of `Local`:

   ```bash
   cmake -B build -DCMAKE_BUILD_TYPE=Release
   cmake --build build --target install
   ```

   > If the Debug version is required, run the following command then:
   >
   > ```
   > cmake -B build -DCMAKE_BUILD_TYPE=Debug
   > cmake --build build --target install
   > ```

3. Copy the `install` in `Local` directory to your install path.

